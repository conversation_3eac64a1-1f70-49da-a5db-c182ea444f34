

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."invoice_status" AS ENUM (
    'pending',
    'paid',
    'overdue'
);


ALTER TYPE "public"."invoice_status" OWNER TO "postgres";


CREATE TYPE "public"."note_type" AS ENUM (
    'OBSERVATION',
    'ACHIEVEMENT',
    'NOTE'
);


ALTER TYPE "public"."note_type" OWNER TO "postgres";


CREATE TYPE "public"."ticket_relationship_type" AS ENUM (
    'blocks',
    'is blocked by',
    'duplicates',
    'is duplicated by',
    'implements',
    'is implemented by',
    'is idea for',
    'relates to'
);


ALTER TYPE "public"."ticket_relationship_type" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
begin
    new.updated_at = timezone('utc'::text, now());
    return new;
end;
$$;


ALTER FUNCTION "public"."handle_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_overdue_invoices"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    UPDATE public.invoices 
    SET status = 'overdue'::invoice_status,
        updated_at = NOW()
    WHERE status = 'pending'::invoice_status 
    AND due_date < CURRENT_DATE;
END;
$$;


ALTER FUNCTION "public"."mark_overdue_invoices"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$;


ALTER FUNCTION "public"."set_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."business_info" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "address_line_1" "text",
    "address_line_2" "text",
    "phone_no" "text",
    "email" "text",
    "country_code" "text",
    "country_name" "text"
);


ALTER TABLE "public"."business_info" OWNER TO "postgres";


ALTER TABLE "public"."business_info" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."business_info_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."comments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "ticket_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "content" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"())
);


ALTER TABLE "public"."comments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."daily_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "employee_id" "uuid" NOT NULL,
    "project_id" "uuid" NOT NULL,
    "date" timestamp with time zone NOT NULL,
    "content" "text" NOT NULL,
    "hours_spent" numeric NOT NULL,
    "attachments" "text"[],
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."daily_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."documents" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "employee_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "path" "text" NOT NULL,
    "signed_url" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."employee_bills" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "employee_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "amount" numeric(10,2) NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."employee_bills" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."employee_notes" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "employee_id" "uuid" NOT NULL,
    "content" "text" NOT NULL,
    "type" "public"."note_type" DEFAULT 'NOTE'::"public"."note_type" NOT NULL,
    "created_by" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."employee_notes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."employee_skills" (
    "employee_id" "uuid" NOT NULL,
    "skill_id" character varying NOT NULL,
    "proficiency" integer NOT NULL,
    "years_of_experience" numeric(4,1) NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "employee_skills_proficiency_check" CHECK ((("proficiency" >= 1) AND ("proficiency" <= 5))),
    CONSTRAINT "employee_skills_years_of_experience_check" CHECK (("years_of_experience" >= (0)::numeric))
);


ALTER TABLE "public"."employee_skills" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."employees" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "other_names" "text",
    "business_email" "text" NOT NULL,
    "role_type" "text" NOT NULL,
    "role_title" "text" NOT NULL,
    "start_date" timestamp with time zone NOT NULL,
    "salary" numeric NOT NULL,
    "currency" "text" NOT NULL,
    "bank_account" "jsonb",
    "github_id" "text",
    "slack_id" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "personal_email" "text",
    "salary_currency" "text" DEFAULT 'NGN'::"text" NOT NULL,
    "end_date" timestamp with time zone,
    "user_id" "uuid",
    CONSTRAINT "employees_salary_currency_check" CHECK (("salary_currency" = ANY (ARRAY['NGN'::"text", 'EUR'::"text", 'GBP'::"text", 'USD'::"text"])))
);


ALTER TABLE "public"."employees" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."feedback" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "employee_id" "uuid" NOT NULL,
    "reviewer_id" "uuid",
    "content" "text" NOT NULL,
    "rating" integer NOT NULL,
    "created_by" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "feedback_rating_check1" CHECK ((("rating" >= 1) AND ("rating" <= 5)))
);


ALTER TABLE "public"."feedback" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."github_prs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "ticket_id" "uuid",
    "repo_full_name" "text" NOT NULL,
    "pr_number" integer NOT NULL,
    "pr_title" "text" NOT NULL,
    "pr_url" "text" NOT NULL,
    "pr_status" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "github_prs_pr_status_check" CHECK (("pr_status" = ANY (ARRAY['open'::"text", 'closed'::"text", 'merged'::"text"])))
);


ALTER TABLE "public"."github_prs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."github_repos" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "project_id" "uuid",
    "repo_owner" "text" NOT NULL,
    "repo_name" "text" NOT NULL,
    "repo_url" "text" NOT NULL,
    "webhook_id" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."github_repos" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."invoices" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "employee_id" "uuid" NOT NULL,
    "amount" numeric NOT NULL,
    "currency" "text" NOT NULL,
    "status" "public"."invoice_status" NOT NULL,
    "issue_date" timestamp with time zone NOT NULL,
    "due_date" timestamp with time zone NOT NULL,
    "paid_date" timestamp with time zone,
    "description" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "invoice_month" "date",
    "pdf_path" "text",
    "uploaded_at" timestamp with time zone DEFAULT "now"(),
    "payment_receipt_path" "text",
    "payment_notes" "text",
    "payment_method" "text"
);


ALTER TABLE "public"."invoices" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."jira_projects" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "project_id" "uuid",
    "jira_key" "text" NOT NULL,
    "jira_id" "text" NOT NULL,
    "jira_url" "text" NOT NULL,
    "webhook_id" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."jira_projects" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."kanban_columns" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "project_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "status" "text" NOT NULL,
    "position" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"())
);


ALTER TABLE "public"."kanban_columns" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."leave_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "employee_id" "uuid" NOT NULL,
    "start_date" timestamp with time zone NOT NULL,
    "end_date" timestamp with time zone NOT NULL,
    "reason" "text" NOT NULL,
    "status" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."leave_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" bigint NOT NULL,
    "name" "text",
    "email" "text",
    "phone" "text",
    "message" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."messages" OWNER TO "postgres";


ALTER TABLE "public"."messages" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."messages_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."portfolio" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "slug" "text" NOT NULL,
    "image_url" "text",
    "summary" "text" NOT NULL,
    "case_study" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "logo" "text",
    "hero" "text",
    "section1" "text"[],
    "section2" "text"[],
    "subtitle" "text",
    "description" "text"
);


ALTER TABLE "public"."portfolio" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."project_github_integrations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "project_id" "uuid" NOT NULL,
    "repo_full_name" "text" NOT NULL,
    "repo_id" "text" NOT NULL,
    "webhook_id" "text",
    "active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."project_github_integrations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."project_jira_integrations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "project_id" "uuid" NOT NULL,
    "jira_project_id" "text" NOT NULL,
    "jira_project_key" "text" NOT NULL,
    "webhook_id" "text",
    "active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."project_jira_integrations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."project_members" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "project_id" "uuid" NOT NULL,
    "employee_id" "uuid",
    "role" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "user_id" "uuid"
);


ALTER TABLE "public"."project_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."project_quotations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "estimatedBudget" "text",
    "modelId" "text",
    "feasibility" "text",
    "recommendation" "jsonb",
    "proj_req_id" "uuid" DEFAULT "gen_random_uuid"(),
    "resourceAssessment" "text",
    "projectPlan" "jsonb"
);


ALTER TABLE "public"."project_quotations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."project_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "phone" "text",
    "email" "text" NOT NULL,
    "type" "jsonb",
    "description" "text",
    "date" "date",
    "projectName" "text",
    "userName" "text",
    "audioFileUrl" "text"
);


ALTER TABLE "public"."project_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."projects" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "start_date" timestamp with time zone NOT NULL,
    "end_date" timestamp with time zone,
    "status" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "project_type" "text" DEFAULT 'INTERNAL'::"text" NOT NULL,
    "client_name" "text",
    "github_repo" "text",
    "key" "text"
);


ALTER TABLE "public"."projects" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."service_tags" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "service_id" "uuid" DEFAULT "gen_random_uuid"(),
    "tags" "text"
);


ALTER TABLE "public"."service_tags" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."services" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "title" "text" DEFAULT ''::"text",
    "description" "text" DEFAULT ''::"text",
    "detailPageDescription" "text" DEFAULT ''::"text",
    "linkTag" "text" DEFAULT ''::"text",
    "service" "text" DEFAULT ''::"text"
);


ALTER TABLE "public"."services" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."skills" (
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "category" character varying NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."skills" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."sub_service" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "title" "text" DEFAULT ''::"text",
    "img" "text",
    "description" "text" DEFAULT ''::"text",
    "icon" "text" DEFAULT ''::"text",
    "detail_information" "text" DEFAULT ''::"text",
    "service_id" "uuid" DEFAULT "gen_random_uuid"()
);


ALTER TABLE "public"."sub_service" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."ticket_assignees" (
    "ticket_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "assigned_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."ticket_assignees" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."ticket_dependencies" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "ticket_id" "uuid" NOT NULL,
    "related_ticket_id" "uuid" NOT NULL,
    "relationship_type" "public"."ticket_relationship_type" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"())
);


ALTER TABLE "public"."ticket_dependencies" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."tickets" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "description" "text" NOT NULL,
    "status" "text" NOT NULL,
    "priority" "text" NOT NULL,
    "type" "text" NOT NULL,
    "assignee_id" "uuid",
    "reporter_id" "uuid" NOT NULL,
    "project_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "due_date" timestamp with time zone,
    "estimated_hours" numeric,
    "labels" "text"[] DEFAULT ARRAY[]::"text"[] NOT NULL,
    "github_prs" "jsonb" DEFAULT '[]'::"jsonb",
    "jira_key" "text",
    "reference" "text",
    CONSTRAINT "tickets_priority_check" CHECK (("priority" = ANY (ARRAY['low'::"text", 'medium'::"text", 'high'::"text", 'urgent'::"text"]))),
    CONSTRAINT "tickets_status_check" CHECK (("status" = ANY (ARRAY['backlog'::"text", 'todo'::"text", 'in-progress'::"text", 'review'::"text", 'done'::"text"]))),
    CONSTRAINT "tickets_type_check" CHECK (("type" = ANY (ARRAY['bug'::"text", 'feature'::"text", 'task'::"text", 'improvement'::"text"])))
);


ALTER TABLE "public"."tickets" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "email" "text" NOT NULL,
    "image_url" "text",
    "role" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "clerk_id" "text",
    "disabled" boolean DEFAULT false NOT NULL,
    CONSTRAINT "users_role_check" CHECK (("role" = ANY (ARRAY['admin'::"text", 'developer'::"text", 'client'::"text"])))
);


ALTER TABLE "public"."users" OWNER TO "postgres";


ALTER TABLE ONLY "public"."business_info"
    ADD CONSTRAINT "business_info_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."comments"
    ADD CONSTRAINT "comments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."daily_logs"
    ADD CONSTRAINT "daily_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."employee_bills"
    ADD CONSTRAINT "employee_bills_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."employee_notes"
    ADD CONSTRAINT "employee_notes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."employee_skills"
    ADD CONSTRAINT "employee_skills_pkey" PRIMARY KEY ("employee_id", "skill_id");



ALTER TABLE ONLY "public"."employees"
    ADD CONSTRAINT "employees_email_key" UNIQUE ("business_email");



ALTER TABLE ONLY "public"."employees"
    ADD CONSTRAINT "employees_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."feedback"
    ADD CONSTRAINT "feedback_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."github_prs"
    ADD CONSTRAINT "github_prs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."github_repos"
    ADD CONSTRAINT "github_repos_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."github_repos"
    ADD CONSTRAINT "github_repos_project_id_repo_owner_repo_name_key" UNIQUE ("project_id", "repo_owner", "repo_name");



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."jira_projects"
    ADD CONSTRAINT "jira_projects_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."jira_projects"
    ADD CONSTRAINT "jira_projects_project_id_key" UNIQUE ("project_id");



ALTER TABLE ONLY "public"."kanban_columns"
    ADD CONSTRAINT "kanban_columns_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."leave_requests"
    ADD CONSTRAINT "leave_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."portfolio"
    ADD CONSTRAINT "portfolio_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."portfolio"
    ADD CONSTRAINT "portfolio_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."project_github_integrations"
    ADD CONSTRAINT "project_github_integrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_github_integrations"
    ADD CONSTRAINT "project_github_integrations_project_id_repo_full_name_key" UNIQUE ("project_id", "repo_full_name");



ALTER TABLE ONLY "public"."project_jira_integrations"
    ADD CONSTRAINT "project_jira_integrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_jira_integrations"
    ADD CONSTRAINT "project_jira_integrations_project_id_key" UNIQUE ("project_id");



ALTER TABLE ONLY "public"."project_members"
    ADD CONSTRAINT "project_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_members"
    ADD CONSTRAINT "project_members_project_id_employee_id_key" UNIQUE ("project_id", "employee_id");



ALTER TABLE ONLY "public"."project_quotations"
    ADD CONSTRAINT "project_quotation_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_requests"
    ADD CONSTRAINT "project_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."projects"
    ADD CONSTRAINT "projects_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_tags"
    ADD CONSTRAINT "service-tags_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."services"
    ADD CONSTRAINT "services_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."skills"
    ADD CONSTRAINT "skills_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sub_service"
    ADD CONSTRAINT "sub-service_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."ticket_assignees"
    ADD CONSTRAINT "ticket_assignees_pkey" PRIMARY KEY ("ticket_id", "user_id");



ALTER TABLE ONLY "public"."ticket_dependencies"
    ADD CONSTRAINT "ticket_dependencies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tickets"
    ADD CONSTRAINT "tickets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_clerk_id_key" UNIQUE ("clerk_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



CREATE INDEX "employee_notes_created_at_idx" ON "public"."employee_notes" USING "btree" ("created_at" DESC);



CREATE INDEX "employee_notes_created_by_idx" ON "public"."employee_notes" USING "btree" ("created_by");



CREATE INDEX "employee_notes_employee_id_idx" ON "public"."employee_notes" USING "btree" ("employee_id");



CREATE INDEX "employee_notes_type_idx" ON "public"."employee_notes" USING "btree" ("type");



CREATE INDEX "feedback_created_at_idx" ON "public"."feedback" USING "btree" ("created_at" DESC);



CREATE INDEX "feedback_created_by_idx" ON "public"."feedback" USING "btree" ("created_by");



CREATE INDEX "feedback_employee_id_idx" ON "public"."feedback" USING "btree" ("employee_id");



CREATE INDEX "feedback_reviewer_id_idx" ON "public"."feedback" USING "btree" ("reviewer_id");



CREATE INDEX "idx_comments_ticket_id" ON "public"."comments" USING "btree" ("ticket_id");



CREATE INDEX "idx_comments_user_id" ON "public"."comments" USING "btree" ("user_id");



CREATE INDEX "idx_documents_employee_id" ON "public"."documents" USING "btree" ("employee_id");



CREATE INDEX "idx_documents_type" ON "public"."documents" USING "btree" ("type");



CREATE INDEX "idx_employees_user_id" ON "public"."employees" USING "btree" ("user_id");



CREATE INDEX "idx_invoices_employee_month" ON "public"."invoices" USING "btree" ("employee_id", "invoice_month");



CREATE INDEX "idx_invoices_status_due_date" ON "public"."invoices" USING "btree" ("status", "due_date");



CREATE INDEX "idx_project_jira_integrations_project_id" ON "public"."project_jira_integrations" USING "btree" ("project_id");



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."portfolio" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



CREATE OR REPLACE TRIGGER "set_updated_at" BEFORE UPDATE ON "public"."employee_notes" FOR EACH ROW EXECUTE FUNCTION "public"."set_updated_at"();



CREATE OR REPLACE TRIGGER "set_updated_at" BEFORE UPDATE ON "public"."feedback" FOR EACH ROW EXECUTE FUNCTION "public"."set_updated_at"();



CREATE OR REPLACE TRIGGER "update_documents_updated_at" BEFORE UPDATE ON "public"."documents" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_github_prs_updated_at" BEFORE UPDATE ON "public"."github_prs" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_github_repos_updated_at" BEFORE UPDATE ON "public"."github_repos" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_jira_projects_updated_at" BEFORE UPDATE ON "public"."jira_projects" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_project_github_integrations_updated_at" BEFORE UPDATE ON "public"."project_github_integrations" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_project_jira_integrations_updated_at" BEFORE UPDATE ON "public"."project_jira_integrations" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_tickets_updated_at" BEFORE UPDATE ON "public"."tickets" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_users_updated_at" BEFORE UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



ALTER TABLE ONLY "public"."comments"
    ADD CONSTRAINT "comments_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."comments"
    ADD CONSTRAINT "comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."daily_logs"
    ADD CONSTRAINT "daily_logs_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."daily_logs"
    ADD CONSTRAINT "daily_logs_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."employee_bills"
    ADD CONSTRAINT "employee_bills_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."employee_notes"
    ADD CONSTRAINT "employee_notes_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."employee_skills"
    ADD CONSTRAINT "employee_skills_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."employee_skills"
    ADD CONSTRAINT "employee_skills_skill_id_fkey" FOREIGN KEY ("skill_id") REFERENCES "public"."skills"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."employees"
    ADD CONSTRAINT "employees_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."feedback"
    ADD CONSTRAINT "feedback_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."feedback"
    ADD CONSTRAINT "feedback_reviewer_id_fkey" FOREIGN KEY ("reviewer_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."github_prs"
    ADD CONSTRAINT "github_prs_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."github_repos"
    ADD CONSTRAINT "github_repos_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."jira_projects"
    ADD CONSTRAINT "jira_projects_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."kanban_columns"
    ADD CONSTRAINT "kanban_columns_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."leave_requests"
    ADD CONSTRAINT "leave_requests_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_github_integrations"
    ADD CONSTRAINT "project_github_integrations_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_jira_integrations"
    ADD CONSTRAINT "project_jira_integrations_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_members"
    ADD CONSTRAINT "project_members_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employees"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_members"
    ADD CONSTRAINT "project_members_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_members"
    ADD CONSTRAINT "project_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."project_quotations"
    ADD CONSTRAINT "project_quotation_proj_req_id_fkey" FOREIGN KEY ("proj_req_id") REFERENCES "public"."project_requests"("id");



ALTER TABLE ONLY "public"."service_tags"
    ADD CONSTRAINT "service-tags_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."services"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE ONLY "public"."sub_service"
    ADD CONSTRAINT "sub-service_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."services"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE ONLY "public"."ticket_assignees"
    ADD CONSTRAINT "ticket_assignees_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."ticket_assignees"
    ADD CONSTRAINT "ticket_assignees_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."ticket_dependencies"
    ADD CONSTRAINT "ticket_dependencies_related_ticket_id_fkey" FOREIGN KEY ("related_ticket_id") REFERENCES "public"."tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."ticket_dependencies"
    ADD CONSTRAINT "ticket_dependencies_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tickets"
    ADD CONSTRAINT "tickets_assignee_id_fkey" FOREIGN KEY ("assignee_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."tickets"
    ADD CONSTRAINT "tickets_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id");



ALTER TABLE ONLY "public"."tickets"
    ADD CONSTRAINT "tickets_reporter_id_fkey" FOREIGN KEY ("reporter_id") REFERENCES "public"."users"("id");



CREATE POLICY "Admins can manage GitHub integrations" ON "public"."project_github_integrations" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view all users" ON "public"."users" FOR SELECT TO "authenticated" USING (("role" = 'admin'::"text"));



CREATE POLICY "Admins or project members can insert tickets" ON "public"."tickets" FOR INSERT TO "authenticated" WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."project_members"
  WHERE (("project_members"."project_id" = "tickets"."project_id") AND ("project_members"."employee_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = 'admin'::"text"))))));



CREATE POLICY "Admins, reporter, or assignee can delete tickets" ON "public"."tickets" FOR DELETE TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = 'admin'::"text")))) OR ("reporter_id" = "auth"."uid"()) OR ("assignee_id" = "auth"."uid"())));



CREATE POLICY "Admins, reporter, or assignee can update tickets" ON "public"."tickets" FOR UPDATE TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = 'admin'::"text")))) OR ("reporter_id" = "auth"."uid"()) OR ("assignee_id" = "auth"."uid"())));



CREATE POLICY "All authenticated users can view GitHub repos" ON "public"."github_repos" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "All authenticated users can view JIRA projects" ON "public"."jira_projects" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Allow all inserts" ON "public"."users" FOR INSERT WITH CHECK (true);



CREATE POLICY "Allow all updates" ON "public"."users" FOR UPDATE USING (true);



CREATE POLICY "Allow authenticated users to manage portfolio" ON "public"."portfolio" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow delete to feedback owner" ON "public"."feedback" FOR DELETE USING (("created_by" = (("current_setting"('request.jwt.claims'::"text"))::"json" ->> 'sub'::"text")));



CREATE POLICY "Allow delete to note owner" ON "public"."employee_notes" FOR DELETE USING (("created_by" = (("current_setting"('request.jwt.claims'::"text"))::"json" ->> 'sub'::"text")));



CREATE POLICY "Allow insert to all" ON "public"."employee_notes" FOR INSERT WITH CHECK (true);



CREATE POLICY "Allow insert to all" ON "public"."feedback" FOR INSERT WITH CHECK (true);



CREATE POLICY "Allow public read access" ON "public"."portfolio" FOR SELECT USING (true);



CREATE POLICY "Allow read access to all" ON "public"."employee_notes" FOR SELECT USING (true);



CREATE POLICY "Allow read access to all" ON "public"."feedback" FOR SELECT USING (true);



CREATE POLICY "Allow update to feedback owner" ON "public"."feedback" FOR UPDATE USING (("created_by" = (("current_setting"('request.jwt.claims'::"text"))::"json" ->> 'sub'::"text")));



CREATE POLICY "Allow update to note owner" ON "public"."employee_notes" FOR UPDATE USING (("created_by" = (("current_setting"('request.jwt.claims'::"text"))::"json" ->> 'sub'::"text")));



CREATE POLICY "Authenticated Users Can Delete Bills" ON "public"."employee_bills" FOR DELETE USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Authenticated Users Can Insert Bills" ON "public"."employee_bills" FOR INSERT WITH CHECK (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Authenticated Users Can Update Bills" ON "public"."employee_bills" FOR UPDATE USING (("auth"."role"() = 'authenticated'::"text")) WITH CHECK (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Authenticated Users Can View Bills" ON "public"."employee_bills" FOR SELECT USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Only admins can manage GitHub repos" ON "public"."github_repos" TO "authenticated" USING (("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = 'admin'::"text"))));



CREATE POLICY "Only admins can manage JIRA projects" ON "public"."jira_projects" TO "authenticated" USING (("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = 'admin'::"text"))));



CREATE POLICY "Project members can view tickets" ON "public"."tickets" FOR SELECT TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."project_members"
  WHERE (("project_members"."project_id" = "tickets"."project_id") AND ("project_members"."employee_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = 'admin'::"text"))))));



CREATE POLICY "Users can view PRs for tickets they can access" ON "public"."github_prs" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."tickets"
  WHERE (("tickets"."id" = "github_prs"."ticket_id") AND ((EXISTS ( SELECT 1
           FROM "public"."project_members"
          WHERE (("project_members"."project_id" = "tickets"."project_id") AND ("project_members"."employee_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
           FROM "public"."users"
          WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = 'admin'::"text")))))))));



ALTER TABLE "public"."employee_bills" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."employee_notes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."feedback" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."github_prs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."github_repos" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."jira_projects" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."portfolio" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."project_github_integrations" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";











































































































































































GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_overdue_invoices"() TO "anon";
GRANT ALL ON FUNCTION "public"."mark_overdue_invoices"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_overdue_invoices"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";


















GRANT ALL ON TABLE "public"."business_info" TO "anon";
GRANT ALL ON TABLE "public"."business_info" TO "authenticated";
GRANT ALL ON TABLE "public"."business_info" TO "service_role";



GRANT ALL ON SEQUENCE "public"."business_info_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."business_info_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."business_info_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."comments" TO "anon";
GRANT ALL ON TABLE "public"."comments" TO "authenticated";
GRANT ALL ON TABLE "public"."comments" TO "service_role";



GRANT ALL ON TABLE "public"."daily_logs" TO "anon";
GRANT ALL ON TABLE "public"."daily_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."daily_logs" TO "service_role";



GRANT ALL ON TABLE "public"."documents" TO "anon";
GRANT ALL ON TABLE "public"."documents" TO "authenticated";
GRANT ALL ON TABLE "public"."documents" TO "service_role";



GRANT ALL ON TABLE "public"."employee_bills" TO "anon";
GRANT ALL ON TABLE "public"."employee_bills" TO "authenticated";
GRANT ALL ON TABLE "public"."employee_bills" TO "service_role";



GRANT ALL ON TABLE "public"."employee_notes" TO "anon";
GRANT ALL ON TABLE "public"."employee_notes" TO "authenticated";
GRANT ALL ON TABLE "public"."employee_notes" TO "service_role";



GRANT ALL ON TABLE "public"."employee_skills" TO "anon";
GRANT ALL ON TABLE "public"."employee_skills" TO "authenticated";
GRANT ALL ON TABLE "public"."employee_skills" TO "service_role";



GRANT ALL ON TABLE "public"."employees" TO "anon";
GRANT ALL ON TABLE "public"."employees" TO "authenticated";
GRANT ALL ON TABLE "public"."employees" TO "service_role";



GRANT ALL ON TABLE "public"."feedback" TO "anon";
GRANT ALL ON TABLE "public"."feedback" TO "authenticated";
GRANT ALL ON TABLE "public"."feedback" TO "service_role";



GRANT ALL ON TABLE "public"."github_prs" TO "anon";
GRANT ALL ON TABLE "public"."github_prs" TO "authenticated";
GRANT ALL ON TABLE "public"."github_prs" TO "service_role";



GRANT ALL ON TABLE "public"."github_repos" TO "anon";
GRANT ALL ON TABLE "public"."github_repos" TO "authenticated";
GRANT ALL ON TABLE "public"."github_repos" TO "service_role";



GRANT ALL ON TABLE "public"."invoices" TO "anon";
GRANT ALL ON TABLE "public"."invoices" TO "authenticated";
GRANT ALL ON TABLE "public"."invoices" TO "service_role";



GRANT ALL ON TABLE "public"."jira_projects" TO "anon";
GRANT ALL ON TABLE "public"."jira_projects" TO "authenticated";
GRANT ALL ON TABLE "public"."jira_projects" TO "service_role";



GRANT ALL ON TABLE "public"."kanban_columns" TO "anon";
GRANT ALL ON TABLE "public"."kanban_columns" TO "authenticated";
GRANT ALL ON TABLE "public"."kanban_columns" TO "service_role";



GRANT ALL ON TABLE "public"."leave_requests" TO "anon";
GRANT ALL ON TABLE "public"."leave_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."leave_requests" TO "service_role";



GRANT ALL ON TABLE "public"."messages" TO "anon";
GRANT ALL ON TABLE "public"."messages" TO "authenticated";
GRANT ALL ON TABLE "public"."messages" TO "service_role";



GRANT ALL ON SEQUENCE "public"."messages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."messages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."messages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."portfolio" TO "anon";
GRANT ALL ON TABLE "public"."portfolio" TO "authenticated";
GRANT ALL ON TABLE "public"."portfolio" TO "service_role";



GRANT ALL ON TABLE "public"."project_github_integrations" TO "anon";
GRANT ALL ON TABLE "public"."project_github_integrations" TO "authenticated";
GRANT ALL ON TABLE "public"."project_github_integrations" TO "service_role";



GRANT ALL ON TABLE "public"."project_jira_integrations" TO "anon";
GRANT ALL ON TABLE "public"."project_jira_integrations" TO "authenticated";
GRANT ALL ON TABLE "public"."project_jira_integrations" TO "service_role";



GRANT ALL ON TABLE "public"."project_members" TO "anon";
GRANT ALL ON TABLE "public"."project_members" TO "authenticated";
GRANT ALL ON TABLE "public"."project_members" TO "service_role";



GRANT ALL ON TABLE "public"."project_quotations" TO "anon";
GRANT ALL ON TABLE "public"."project_quotations" TO "authenticated";
GRANT ALL ON TABLE "public"."project_quotations" TO "service_role";



GRANT ALL ON TABLE "public"."project_requests" TO "anon";
GRANT ALL ON TABLE "public"."project_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."project_requests" TO "service_role";



GRANT ALL ON TABLE "public"."projects" TO "anon";
GRANT ALL ON TABLE "public"."projects" TO "authenticated";
GRANT ALL ON TABLE "public"."projects" TO "service_role";



GRANT ALL ON TABLE "public"."service_tags" TO "anon";
GRANT ALL ON TABLE "public"."service_tags" TO "authenticated";
GRANT ALL ON TABLE "public"."service_tags" TO "service_role";



GRANT ALL ON TABLE "public"."services" TO "anon";
GRANT ALL ON TABLE "public"."services" TO "authenticated";
GRANT ALL ON TABLE "public"."services" TO "service_role";



GRANT ALL ON TABLE "public"."skills" TO "anon";
GRANT ALL ON TABLE "public"."skills" TO "authenticated";
GRANT ALL ON TABLE "public"."skills" TO "service_role";



GRANT ALL ON TABLE "public"."sub_service" TO "anon";
GRANT ALL ON TABLE "public"."sub_service" TO "authenticated";
GRANT ALL ON TABLE "public"."sub_service" TO "service_role";



GRANT ALL ON TABLE "public"."ticket_assignees" TO "anon";
GRANT ALL ON TABLE "public"."ticket_assignees" TO "authenticated";
GRANT ALL ON TABLE "public"."ticket_assignees" TO "service_role";



GRANT ALL ON TABLE "public"."ticket_dependencies" TO "anon";
GRANT ALL ON TABLE "public"."ticket_dependencies" TO "authenticated";
GRANT ALL ON TABLE "public"."ticket_dependencies" TO "service_role";



GRANT ALL ON TABLE "public"."tickets" TO "anon";
GRANT ALL ON TABLE "public"."tickets" TO "authenticated";
GRANT ALL ON TABLE "public"."tickets" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
