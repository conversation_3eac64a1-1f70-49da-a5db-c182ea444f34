# Database Migrations Guide

This document outlines the best practices for managing database migrations in a team environment.

## Team Sync Best Practices

### Before Starting Work
1. Always pull the latest changes from the main branch:
   ```bash
   git pull origin main
   ```

2. Check for new migrations:
   ```bash
   npx supabase migration list
   ```

### Creating Migrations
1. Create a new migration for any database changes:
   ```bash
   npx supabase migration new descriptive_name
   ```

2. Write idempotent SQL in your migration files.

3. Test migrations locally before pushing:
   ```bash
   npx supabase db reset
   ```

### When You Encounter Migration Issues

#### Missing Migration Files
If you see an error about missing migration files:

1. Get the latest migrations from the team:
   ```bash
   git pull origin main
   ```

2. If the issue persists, check the remote migration history:
   ```bash
   npx supabase migration list --linked
   ```

3. For a specific missing migration, repair the status:
   ```bash
   npx supabase migration repair --status reverted VERSION_NUMBER
   npx supabase db push
   ```

#### Migration Conflicts
If there are conflicts in migration history:

1. Back up your local database if needed.
2. Reset your local migrations:
   ```bash
   rm -rf supabase/migrations/*
   npx supabase migration new init
   npx supabase db pull
   ```
3. Commit and push the new migration file.

## CI/CD Integration

The GitHub workflow includes these safety checks:

1. **Pre-flight Check**: Verifies all required migrations exist before applying them.
2. **Force Push**: Uses `--force` flag to handle minor version mismatches.
3. **Failure Handling**: Provides clear error messages when migrations are missing.

## Common Issues and Solutions

### "Remote migration versions not found in local migrations directory"
This occurs when the remote database has migrations that aren't in your local directory.

**Solution**:
```bash
# Get the missing version from the error message
npx supabase migration repair --status reverted VERSION_NUMBER
npx supabase db push
```

### "Migration failed: relation already exists"
This happens when migrations are out of sync with the database state.

**Solution**:
```bash
# Reset your local database state
npx supabase db reset
```

## Best Practices

1. **Always commit migration files** to version control.
2. **Never modify** committed migration files.
3. **Test migrations** in a development environment first.
4. **Use descriptive names** for migration files.
5. **Keep migrations small** and focused on a single change.

## Emergency Procedures

If you need to recover from a failed migration in production:

1. Revert the deployment if possible.
2. Fix the migration file.
3. Create a new migration to apply the fix.
4. Test thoroughly before redeploying.
