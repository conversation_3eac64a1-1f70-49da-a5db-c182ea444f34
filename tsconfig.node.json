{"compilerOptions": {"target": "ES2022", "lib": ["ES2023", "DOM"], "module": "ESNext", "moduleResolution": "NodeNext", "skipLibCheck": true, "paths": {"@/*": ["./src/*"]}, "isolatedModules": true, "esModuleInterop": true, "resolveJsonModule": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": true}, "include": ["vite.config.ts"], "exclude": ["node_modules", "api", "lib", "src"]}