# kanban

## Project info

## Repository Initialization Notice

## How can I edit this code?

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone https://github.com/sparkstrand/kanban.git

# Step 2: Navigate to the project directory.
cd kanban

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## Environment Variables for JIRA Integration

To enable JIRA integration, the following environment variables must be set in the environment (or `.env` file for local development):

-   `JIRA_BASE_URL`: The base URL of the JIRA instance (e.g., `https://sparkstrand.atlassian.net`).
-   `JIRA_EMAIL`: The email address of the JIRA user account that will be used for API interactions. This user should have appropriate permissions in JIRA to create/update issues, comments, and projects as needed.
-   `JIRA_API_KEY`: The API token generated for the JIRA user account specified in `JIRA_EMAIL`. (In JIRA, this is typically created under Account Settings -> Security -> Create and manage API tokens).
## Setting up JIRA Integration

To set up the JIRA integration, our organization needs to configure the following environment variables:

-   `JIRA_BASE_URL`: The base URL of our JIRA instance (e.g., `https://sparkstrand.atlassian.net`).
-   `JIRA_EMAIL`: The email address of the JIRA user account that will be used for API interactions. This user should have appropriate permissions in JIRA to create/update issues, comments, and projects as needed.
-   `JIRA_API_KEY`: The API token generated for the JIRA user account specified in `JIRA_EMAIL`. You can create an API token in your JIRA account settings under "Security" > "Create and manage API tokens".

### Webhooks

To enable real-time syncing from JIRA to the Kanban board, we need to set up a webhook in our JIRA instance.

1.  In JIRA, go to "JIRA settings" (the gear icon) > "System" > "Webhooks".
2.  Click "Create a webhook".
3.  Enter a name for the webhook (e.g., "Kanban Integration").
4.  For the URL, enter the URL of our deployed application's JIRA webhook endpoint (e.g., `https://our-app.vercel.app/api/webhooks/jira`).
5.  Select the following events:
    *   **Issue**: `created`, `updated`, `deleted`
    *   **Comment**: `created`, `updated`, `deleted`
    *   **Project**: `created`
6.  Click "Create".

### Important Notes

*   The `lead` for a new project is automatically determined by the email of the account specified in the `JIRA_EMAIL` environment variable.

**Example `.env` file:**

```
JIRA_BASE_URL=https://sparkstrand.atlassian.net
JIRA_EMAIL=<EMAIL>
JIRA_API_KEY=ATATT3xFfGF0IGnE9aU0yu0RV51eae-Lrd1wCCW4wTamhjecwBLIa6PpvBWDuwqqD5pCWhUXmBfH5tZB69SHqX1OBlGHs6lTUKyQXBjf3cKeafsV-pnhCFTOAFNS6HYWgjwXuavsK9RMpzY7WKZz5B2oPVqf9cY5pv709MNBCyREziImLIq0j7U=1013AA99
```

Ensure these variables are configured correctly for the JIRA synchronization features to work.
