{"name": "kanban", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:api && vite build", "test": "echo \"No tests specified\" && exit 0", "build:api": "tsc -p api/tsconfig.json && tsc-alias -p api/tsconfig.json", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.32.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@octokit/rest": "^22.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/postcss": "^4.1.10", "@tanstack/react-query": "^5.80.7", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lovable-tagger": "^1.1.8", "lucide-react": "^0.515.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.0", "react-resizable-panels": "^3.0.3", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-animated": "^2.0.0", "vaul": "^1.1.2", "winston": "^3.17.0", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/winston": "^2.4.4", "@vercel/node": "^5.2.2", "@vitejs/plugin-react": "^4.5.2", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.5", "supabase": "^1.153.4", "tailwindcss": "^4.1.10", "tsc-alias": "^1.8.16", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0", "vite": "^5.4.2"}, "overrides": {"react": "^19.1.0", "react-dom": "^19.1.0"}}