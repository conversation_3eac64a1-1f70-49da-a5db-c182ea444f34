export interface IJiraUser {
  accountId: string;
  displayName: string;
  emailAddress: string;
  key?: string;
  active?: boolean;
  avatarUrls?: {
    "16x16"?: string;
    "24x24"?: string;
    "32x32"?: string;
    "48x48"?: string;
  };
  timeZone?: string;
  self?: string;
}

export interface IJiraProject {
  id: string;
  key: string;
  name: string;
  description?: string;
  projectTypeKey?: string;
  lead?: IJiraUser;
}

export interface IJiraIssueType {
  id: string;
  name: string;
  description?: string;
  iconUrl?: string;
  subtask?: boolean;
}

export interface IJiraPriority {
  id: string;
  name: string;
  iconUrl?: string;
}

export interface JiraAdfNode {
  type: string;
  content?: JiraAdfNode[];
  text?: string;
  attrs?: any;
}

export interface JiraAdfDoc {
  version: 1;
  type: "doc";
  content: JiraAdfNode[];
}

export interface IJiraComment {
  id: string;
  body: string | JiraAdfDoc;
  author: IJiraUser;
  created: string;
  updated?: string;
  updateAuthor?: IJiraUser;
  self?: string;
  jsdPublic?: boolean;
}

export interface IJiraIssue {
  id: string;
  key: string;
  fields: {
    assignee?: IJiraUser | null;
    reporter?: IJiraUser | null;
    summary: string;
    description?: string | JiraAdfDoc | null;
    comment?: {
      comments: IJiraComment[];
      maxResults?: number;
      total?: number;
      startAt?: number;
    };
    status: {
      name: string;
    };
    project: Pick<IJiraProject, "id" | "key" | "name" | "projectTypeKey">;
    priority?: {
      name: string;
    };
    issuetype: {
      name: string;
      id?: string;
    };
    labels?: string[];
  };
}

export interface IJiraTransition {
  id: string;
  name: string;
  to: {
    id: string;
    name: string;
    statusCategory: {
      name: string;
    };
  };
}

export interface IJiraChangelogItem {
  field: string;
  fromString?: string;
  toString?: string;
}

export interface IJiraWebhookEvent {
  webhookEvent: string;
  issue?: IJiraIssue;
  user?: IJiraUser;
  comment?: IJiraComment;
  changelog?: {
    items: IJiraChangelogItem[];
  };
  project?: IJiraProject;
}

export interface IJiraStatus {
  id: string;
  name: string;
  statusCategory: {
    id: string;
    name: string;
    key: string;
    colorName: string;
  };
}

export interface IJiraClient {
  getAuthHeadersWithContentType: () => { [key: string]: string };
  handleResponse: <T>(response: Response) => Promise<T>;
  createProject(project: {
    name: string;
    key: string;
    projectTypeKey: string;
    lead: string;
    templateKey?: string;
  }): Promise<{ id: string; key: string }>;
  getProject(projectKey: string): Promise<IJiraProject>;
  findUserByEmail(email: string): Promise<IJiraUser | null>;
  createIssue(issue: {
    fields: {
      project: { key: string };
      issuetype: { name: string };
      summary: string;
      description?: any;
    };
  }): Promise<IJiraIssue>;
  updateIssue(
    issueKey: string,
    update: {
      fields?: {
        summary?: string;
        description?: any;
      };
    }
  ): Promise<void>;
  getIssue(issueKey: string): Promise<IJiraIssue>;
  createOrUpdateIssue(
    appTicketData: any,
    projectKey: string,
    issueType: string
  ): Promise<IJiraIssue>;
  getAvailableTransitions(issueKeyOrId: string): Promise<IJiraTransition[]>;
  transitionIssue(issueKeyOrId: string, transitionId: string): Promise<void>;
  addComment(
    issueKey: string,
    comment: { body: string }
  ): Promise<IJiraComment>;
}