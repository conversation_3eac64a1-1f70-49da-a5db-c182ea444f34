export interface IKanbanTask {
  id: string;
  title: string;
  description?: string;
  type: string;
  status: string;
  jiraId?: string;
  labels: string[];
  priority: string;
  projectKey?: string;
  metadata?: {
    syncOrigin?: "jira" | "kanban";
    lastSynced?: string;
  };
}

export interface IKanbanTaskMetadata {
  syncOrigin?: "jira" | "kanban";
  lastSynced?: string;
}

export interface IKanbanProject {
  id: string;
  name: string;
  key?: string;
  description?: string;
  jiraKey?: string;
  syncEnabled: boolean;
}

export interface IKanbanComment {
  id: string;
  content: string;
  author: string;
  createdAt: string;
  updatedAt: string;
  taskId: string;
  jiraCommentId?: string;
}
