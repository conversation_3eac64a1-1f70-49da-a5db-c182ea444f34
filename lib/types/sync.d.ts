 interface ISyncConfig {
    jiraToKanban: {
      statusMap: Record<string, string>;
      fieldMap: {
        summary: 'title';
        description?: 'description';
        issuetype?: 'type';
        priority?: 'priority';
      };
    };
    kanbanToJira: {
      statusMap: Record<string, string>;
    };
  }

export  interface ISyncResult {
    success: boolean;
    direction: 'jira-to-kanban' | 'kanban-to-jira';
    itemCount: number;
    errors?: ISyncError[];
  }
  
 interface ISyncError {
    entityId: string;
    message: string;
    timestamp: string;
  }