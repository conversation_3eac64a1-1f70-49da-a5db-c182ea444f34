import { IJiraComment, IJiraIssue, IJiraIssueUpdate, IJiraProject } from "./jira";

export interface IJiraClient {
    getProject(projectKey: string): Promise<IJiraProject>;
    createIssue(issueData: IJiraCreateIssue): Promise<IJiraIssue>;
    updateIssue(issueId: string, updateData: IJiraIssueUpdate): Promise<void>;
    addComment(issueId: string, comment: IJiraCommentInput): Promise<IJiraComment>;
  }
  
 export interface IJiraCreateIssue {
    fields: {
      project: { key: string };
      summary: string;
      description?: string;
      issuetype: { name: 'Task' | 'Story' | 'Bug' };
    };
  }
  
 export interface IJiraCommentInput {
    body: string;
  }