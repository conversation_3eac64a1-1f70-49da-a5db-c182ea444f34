import { create } from "zustand";

import type {
  Ticket,
  TicketStatus,
  TicketPriority,
  TicketType,
  UserProfile,
  Comment as AppComment,
  GitHubPR,
} from "../../../src/types/index.js";
import { IJiraIssue, IKanbanComment, ISyncResult } from "../../types";
import { syncService } from "../../jira/services/SyncService.js";
import { getSupabaseClient } from "../../../src/integrations/supabase/client.js";
import JiraClient from "../../jira/clients/JiraClient";
import {
  mapJiraStatusToKanban,
  mapKanbanStatusToJira,
  mapJiraPriorityToKanban,
  mapKanbanPriorityToJira,
  mapJiraTypeToKanban,
  mapKanbanTypeToJira,
} from "../../jira/utils/mappers";
import { userMappingService } from "../../jira/services/UserMappingService";
import type {
  Database,
  Tables,
  TablesInsert,
  TablesUpdate,
} from "../../../src/integrations/supabase/types";
import { Json } from "../../../src/integrations/supabase/types";

const supabase = getSupabaseClient(null);

type DbTicketWithJoins = Tables<"tickets"> & {
  assignee: Pick<Tables<"users">, "id" | "name"> | null;
  reporter: Pick<Tables<"users">, "id" | "name"> | null;
};

interface DbTicketCommentRow {
  id: string;
  ticket_id: string;
  content: string;
  author_name: string | null;
  user_id: string | null;
  created_at: string;
  updated_at: string | null;
  jira_comment_id: string | null;
}

interface KanbanState {
  tickets: Map<string, Ticket>;
  isLoading: boolean;
  error: string | null;
  currentTicket: Ticket | null;

  getTicket: (id: string) => Promise<Ticket | undefined>;
  createTicket: (
    ticket: Omit<Ticket, "id" | "createdAt" | "updatedAt">
  ) => Promise<Ticket>;
  updateTicket: (id: string, updates: Partial<Ticket>) => Promise<Ticket>;
  updateTicketStatus: (id: string, status: TicketStatus) => Promise<Ticket>;
  deleteTicket: (id: string) => Promise<void>;
  addComment: (
    ticketId: string,
    comment: Omit<AppComment, "id" | "ticketId" | "createdAt" | "updatedAt"> & {
      jiraCommentId?: string;
    }
  ) => Promise<AppComment>;
  updateCommentByJiraId: (
    jiraIssueKey: string,
    jiraCommentId: string,
    updates: { content: string; authorName?: string }
  ) => Promise<boolean>;
  deleteCommentByJiraId: (
    jiraIssueKey: string,
    jiraCommentId: string
  ) => Promise<boolean>;

  getProjectTickets: (projectId: string) => Promise<Ticket[]>;

  syncFromJira: (jiraIssue: IJiraIssue) => Promise<Ticket>;
  syncToJira: (ticket: Ticket) => Promise<ISyncResult>;
  deleteTicketByJiraId: (jiraIssueKey: string) => Promise<boolean>;
}

const TICKET_SELECT_COLUMNS = `
  *,
  assignee:users!tickets_assignee_id_fkey(id, name),
  reporter:users!tickets_reporter_id_fkey(id, name)
`;

export const useKanbanStore = create<KanbanState>((set, get) => ({
  tickets: new Map(),
  isLoading: false,
  error: null,
  currentTicket: null,

  getTicket: async (id) => {
    const cached = get().tickets.get(id);
    if (cached) return cached;

    set({ isLoading: true });
    try {
      const { data, error } = await supabase
        .from("tickets")
        .select(TICKET_SELECT_COLUMNS)
        .eq("id", id)
        .single();

      if (error) throw error;
      if (!data) return undefined;

      const ticket = mapDbTicketToAppTicket(data);
      set((state) => ({
        tickets: new Map(state.tickets).set(id, ticket),
        isLoading: false,
      }));
      return ticket;
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : "Failed to fetch ticket",
        isLoading: false,
      });
      return undefined;
    }
  },

  createTicket: async (ticket: Omit<Ticket, 'id' | 'createdAt' | 'updatedAt'> & { project_id?: string }) => {
    set({ isLoading: true });
    try {
      // Handle both projectId and project_id for backward compatibility
      const projectId = 'projectId' in ticket && ticket.projectId 
        ? ticket.projectId 
        : 'project_id' in ticket && ticket.project_id 
          ? ticket.project_id 
          : null;
      
      if (!projectId) {
        throw new Error('Project ID is required');
      }
      
      // Type assertion to handle the field name differences
      const ticketWithAny = ticket as any;
      
      const insertPayload: TablesInsert<"tickets"> = {
        title: ticket.title,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        type: ticket.type,
        assignee_id: ticketWithAny.assigneeId || ticketWithAny.assignee_id || null,
        reporter_id: ticketWithAny.reporter_id || null,
        project_id: projectId,
        due_date: ticket.dueDate || null,
        estimated_hours: ticket.estimatedHours || null,
        labels: ticket.labels,
        github_prs: (ticket.githubPRs as unknown as Json) || null,
        jira_key: ticket.jiraId || null,
      };
      const { data, error } = await supabase
        .from("tickets")
        .insert(insertPayload)
        .select(TICKET_SELECT_COLUMNS)
        .single();

      if (error) throw error;
      const newTicket = mapDbTicketToAppTicket(data);
      set((state) => ({
        tickets: new Map(state.tickets).set(newTicket.id, newTicket),
        isLoading: false,
      }));
      return newTicket;
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : "Failed to create ticket",
        isLoading: false,
      });
      throw error;
    }
  },

  updateTicket: async (id, updates) => {
    set({ isLoading: true });
    try {
      const updatePayload: TablesUpdate<"tickets"> = {
        updated_at: new Date().toISOString(),
      };
      if (updates.title !== undefined) updatePayload.title = updates.title;
      if (updates.description !== undefined)
        updatePayload.description = updates.description;
      if (updates.status !== undefined) updatePayload.status = updates.status;
      if (updates.priority !== undefined)
        updatePayload.priority = updates.priority;
      if (updates.type !== undefined) updatePayload.type = updates.type;
      if (updates.assigneeId !== undefined)
        updatePayload.assignee_id = updates.assigneeId;
      if (updates.dueDate !== undefined)
        updatePayload.due_date = updates.dueDate || null;
      if (updates.estimatedHours !== undefined)
        updatePayload.estimated_hours = updates.estimatedHours || null;
      if (updates.labels !== undefined) updatePayload.labels = updates.labels;
      if (updates.githubPRs !== undefined)
        updatePayload.github_prs =
          (updates.githubPRs as unknown as Json) || null;
      if (updates.jiraId !== undefined) updatePayload.jira_key = updates.jiraId;

      const { data, error } = await supabase
        .from("tickets")
        .update(updatePayload)
        .eq("id", id)
        .select(TICKET_SELECT_COLUMNS)
        .single();

      if (error) throw error;

      const updatedTicket = mapDbTicketToAppTicket(data);
      set((state) => ({
        tickets: new Map(state.tickets).set(id, updatedTicket),
        currentTicket:
          state.currentTicket?.id === id ? updatedTicket : state.currentTicket,
        isLoading: false,
      }));

      if (updatedTicket.jiraId && updates.status !== undefined) {
        await syncService.syncKanbanStatusToJira(
          updatedTicket.jiraId,
          updatedTicket.status
        );
      }
      return updatedTicket;
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : "Failed to update ticket",
        isLoading: false,
      });
      throw error;
    }
  },

  updateTicketStatus: async (id, status) => {
    return get().updateTicket(id, { status });
  },

  deleteTicket: async (id) => {
    set({ isLoading: true });
    try {
      const { error } = await supabase.from("tickets").delete().eq("id", id);

      if (error) throw error;

      set((state) => {
        const tickets = new Map(state.tickets);
        tickets.delete(id);
        return {
          tickets,
          currentTicket:
            state.currentTicket?.id === id ? null : state.currentTicket,
          isLoading: false,
        };
      });
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : "Failed to delete ticket",
        isLoading: false,
      });
      throw error;
    }
  },

  getProjectTickets: async (projectId) => {
    set({ isLoading: true });
    try {
      const { data, error } = await supabase
        .from("tickets")
        .select(TICKET_SELECT_COLUMNS)
        .eq("project_id", projectId)
        .order("created_at", { ascending: false });

      if (error) throw error;

      const tickets = data.map(mapDbTicketToAppTicket);
      set((state) => {
        const newTickets = new Map(state.tickets);
        tickets.forEach((t: Ticket) => newTickets.set(t.id, t));
        return {
          tickets: newTickets,
          isLoading: false,
        };
      });
      return tickets;
    } catch (error) {
      set({
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch project tickets",
        isLoading: false,
      });
      throw error;
    }
  },

  addComment: async (ticketId, commentData) => {
    set({ isLoading: true, error: null });
    let newCommentForSync: AppComment | null = null;

    try {
      if (commentData.jiraCommentId) {
        const { data: existingComments, error: findError } = await supabase
          .from("comments")
          .select("*")
          .eq("ticket_id", ticketId)
          .eq("jira_comment_id", commentData.jiraCommentId)
          .limit(1);

        if (findError) {
          console.error(
            "[kanbanStore.addComment] Error finding existing Jira comment:",
            findError
          );
        }

        if (existingComments && existingComments.length > 0) {
          const existingDbComment =
            existingComments[0] as unknown as DbTicketCommentRow;
          const existingAppComment: AppComment = {
            id: existingDbComment.id,
            ticketId: existingDbComment.ticket_id,
            content: existingDbComment.content,
            authorName: existingDbComment.author_name ?? undefined,
            userId: existingDbComment.user_id,
            user: null,
            createdAt: existingDbComment.created_at,

            updatedAt:
              existingDbComment.updated_at ?? existingDbComment.created_at,
            jiraCommentId: existingDbComment.jira_comment_id ?? undefined,
          };
          set({ isLoading: false });
          return existingAppComment;
        }
      }

      const insertCommentPayload = {
        ticket_id: ticketId,
        content: commentData.content,
        author_name:
          commentData.authorName || commentData.user?.name || "Unknown User",
        user_id: commentData.userId || null,
        jira_comment_id: commentData.jiraCommentId || null,

        created_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from("comments")
        .insert(insertCommentPayload)
        .select()
        .single();

      if (error) throw error;
      const dbComment = data as DbTicketCommentRow;

      const newComment = mapDbCommentToAppComment(
        dbComment,
        commentData.user || null
      );
      newCommentForSync = newComment;

      const ticket = get().tickets.get(ticketId);
      if (ticket) {
        const updatedTicket = {
          ...ticket,
          comments: [...(ticket.comments || []), newComment],
        };
        set((state) => ({
          tickets: new Map(state.tickets).set(ticketId, updatedTicket),
        }));
      }
      set({ isLoading: false });

      return newComment;
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "Failed to add comment";
      set({ error: message, isLoading: false });
      throw new Error(message);
    } finally {
      const ticket = get().tickets.get(ticketId);

      if (
        ticket?.jiraId &&
        newCommentForSync &&
        !newCommentForSync.jiraCommentId
      ) {
        const kanbanCommentForSync: IKanbanComment = {
          id: newCommentForSync.id,
          taskId: ticket.id,
          content: newCommentForSync.content,
          author: newCommentForSync.user?.name ?? "",
          createdAt: newCommentForSync.createdAt,
          updatedAt: newCommentForSync.updatedAt,
        };
        await syncService.syncKanbanCommentToJira(
          ticket.jiraId,
          kanbanCommentForSync
        );
      }
    }
  },

  syncFromJira: async (jiraIssue) => {
    const existing = Array.from(get().tickets.values()).find(
      (t) => t.jiraId === jiraIssue.key
    );

    const descriptionFromJira = jiraIssue.fields.description;
    let appDescription = "";
    if (typeof descriptionFromJira === "string") {
      appDescription = descriptionFromJira;
    } else if (
      descriptionFromJira &&
      typeof descriptionFromJira === "object" &&
      descriptionFromJira.type === "doc"
    ) {
      appDescription =
        descriptionFromJira.content
          ?.map((node: any) =>
            node.content?.map((textNode: any) => textNode.text).join("")
          )
          .join("\n") || "";
    }

    let kanbanAssigneeId: string | null = null;
    if (jiraIssue.fields.assignee) {
      const kanbanAssignee = await userMappingService.findKanbanUserByJiraUser(
        jiraIssue.fields.assignee
      );
      kanbanAssigneeId = kanbanAssignee ? kanbanAssignee.id : null;
    }

    let kanbanReporterId: string | null = null;
    if (jiraIssue.fields.reporter) {
      const kanbanReporter = await userMappingService.findKanbanUserByJiraUser(
        jiraIssue.fields.reporter
      );
      kanbanReporterId = kanbanReporter ? kanbanReporter.id : null;
    }

    const ticketData: Omit<Ticket, "id" | "createdAt" | "updatedAt" | "position" | "assignees"> = {
      title: jiraIssue.fields.summary,
      description: appDescription,
      status: mapJiraStatusToKanban(jiraIssue.fields.status.name),
      priority: mapJiraPriorityToKanban(jiraIssue.fields.priority?.name),
      type: mapJiraTypeToKanban(jiraIssue.fields.issuetype?.name) as TicketType,
      projectId: jiraIssue.fields.project.key,
      labels: jiraIssue.fields.labels || [],
      jiraId: jiraIssue.key,
      assigneeId: kanbanAssigneeId,
      reporter_id: kanbanReporterId,
      assignee: null,
      reporter: null,
      comments: [],
      githubPRs: []
    } as unknown as Omit<Ticket, "id" | "createdAt" | "updatedAt" | "position" | "assignees">;

    if (existing) {
      return get().updateTicket(existing.id, ticketData as Partial<Ticket>);
    }
    
    // Ensure we have all required fields for ticket creation
    const projectId = ticketData.projectId || jiraIssue.fields.project.key;
    if (!projectId) {
      throw new Error('Project ID is required for Jira sync');
    }
    
    const ticketToCreate = {
      ...ticketData,
      projectId: projectId,
      project_id: projectId // Include both for backward compatibility
    } as Omit<Ticket, "id" | "createdAt" | "updatedAt">;
    
    return get().createTicket(ticketToCreate);
  },

  syncToJira: async (ticket) => {
    if (!ticket.jiraId) {
      const jiraClient = new JiraClient();
      
      // Ensure we have a valid project key
      const projectKey = ticket.project_id || ticket.projectId;
      if (!projectKey) {
        throw new Error('Project ID is required for Jira sync');
      }
      
      const result = await jiraClient.createIssue({
        fields: {
          project: { key: projectKey },
          summary: ticket.title,
          description: ticket.description,
          issuetype: { name: mapKanbanTypeToJira(ticket.type) },
          priority: { name: mapKanbanPriorityToJira(ticket.priority) },
          labels: ticket.labels,
        },
      });

      const updatedTicket = await get().updateTicket(ticket.id, {
        jiraId: result.key,
      });

      return {
        success: true,
        action: "create",
        entityId: ticket.id,
        jiraReference: result.key,
        direction: "kanban-to-jira",
        itemCount: 1,
      };
    } else {
      const jiraClient = new JiraClient();

      await jiraClient.updateIssue(ticket.jiraId, {
        fields: {
          summary: ticket.title,
          description: ticket.description,

          priority: { name: mapKanbanPriorityToJira(ticket.priority) },
          labels: ticket.labels,
        },
      });

      return {
        success: true,
        action: "update",
        entityId: ticket.id,
        jiraReference: ticket.jiraId,
        direction: "kanban-to-jira",
        itemCount: 1,
      };
    }
  },

  deleteTicketByJiraId: async (jiraIssueKey: string): Promise<boolean> => {
    set({ isLoading: true, error: null });
    try {
      const { data: ticketsFound, error: findError } = await supabase
        .from("tickets")
        .select("id")
        .eq("jira_key", jiraIssueKey)
        .limit(1);

      if (findError) throw findError;

      if (!ticketsFound || ticketsFound.length === 0) {
        set({ isLoading: false });
        return false;
      }

      const ticketToDeleteId = ticketsFound[0].id;

      const { error: commentDeleteError } = await supabase
        .from("comments")
        .delete()
        .eq("ticket_id", ticketToDeleteId);

      if (commentDeleteError) {
        console.error(
          `[kanbanStore] Error deleting comments for ticket ${ticketToDeleteId}:`,
          commentDeleteError
        );
      }

      const { error: ticketDeleteError } = await supabase
        .from("tickets")
        .delete()
        .eq("id", ticketToDeleteId);

      if (ticketDeleteError) throw ticketDeleteError;

      set((state) => {
        const newTickets = new Map(state.tickets);
        newTickets.delete(ticketToDeleteId);
        return {
          tickets: newTickets,
          currentTicket:
            state.currentTicket?.id === ticketToDeleteId
              ? null
              : state.currentTicket,
          isLoading: false,
        };
      });
      return true;
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : "Failed to delete ticket by JIRA key";
      set({ error: message, isLoading: false });
      return false;
    }
  },

  updateCommentByJiraId: async (
    jiraIssueKey: string,
    jiraCommentId: string,
    updates: { content: string; authorName?: string }
  ): Promise<boolean> => {
    set({ isLoading: true, error: null });
    try {
      const { data: ticketsFound, error: findTicketError } = await supabase
        .from("tickets")
        .select("id")
        .eq("jira_key", jiraIssueKey)
        .limit(1);

      if (findTicketError) throw findTicketError;
      if (!ticketsFound || ticketsFound.length === 0) {
        console.warn(
          `[kanbanStore.updateCommentByJiraId] Ticket not found for JIRA key: ${jiraIssueKey}`
        );
        set({ isLoading: false });
        return false;
      }
      const localTicketId = ticketsFound[0].id;

      const { data: updatedComments, error: updateError } = await supabase
        .from("comments")
        .update({
          content: updates.content,
          author_name: updates.authorName,
          updated_at: new Date().toISOString(),
        })
        .eq("ticket_id", localTicketId)
        .eq("jira_comment_id", jiraCommentId)
        .select();

      if (updateError) throw updateError;

      if (!updatedComments || updatedComments.length === 0) {
        console.warn(
          `[kanbanStore.updateCommentByJiraId] Comment not found for JIRA comment ID: ${jiraCommentId} on ticket ${localTicketId}`
        );
        set({ isLoading: false });
        return false;
      }

      set({ isLoading: false });
      return true;
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : "Failed to update comment by JIRA ID";
      set({ error: message, isLoading: false });
      return false;
    }
  },

  deleteCommentByJiraId: async (
    jiraIssueKey: string,
    jiraCommentId: string
  ): Promise<boolean> => {
    set({ isLoading: true, error: null });
    try {
      const { data: ticketsFound, error: findTicketError } = await supabase
        .from("tickets")
        .select("id")
        .eq("jira_key", jiraIssueKey)
        .limit(1);

      if (findTicketError) throw findTicketError;
      if (!ticketsFound || ticketsFound.length === 0) return false;
      const localTicketId = ticketsFound[0].id;

      const { error: deleteError } = await supabase
        .from("comments")
        .delete()
        .eq("ticket_id", localTicketId)
        .eq("jira_comment_id", jiraCommentId);

      if (deleteError) throw deleteError;

      set({ isLoading: false });
      return true;
    } catch (error) {
      set({
        error:
          error instanceof Error
            ? error.message
            : "Failed to delete comment by JIRA ID",
        isLoading: false,
      });
      return false;
    }
  },
}));

function mapDbCommentToAppComment(
  dbComment: DbTicketCommentRow,
  userProfile: UserProfile | null
): AppComment {
  return {
    id: dbComment.id,
    ticketId: dbComment.ticket_id,
    content: dbComment.content,
    authorName: dbComment.author_name ?? undefined,
    userId: dbComment.user_id,
    user: userProfile,
    createdAt: dbComment.created_at,
    updatedAt: dbComment.updated_at ?? dbComment.created_at,
    jiraCommentId: dbComment.jira_comment_id ?? undefined,
  };
}

function mapDbTicketToAppTicket(dbTicket: DbTicketWithJoins): Ticket {
  return {
    id: dbTicket.id,
    title: dbTicket.title,
    description: dbTicket.description || "",
    status: dbTicket.status as TicketStatus,
    priority: dbTicket.priority as TicketPriority,
    type: dbTicket.type as TicketType,
    assigneeId: dbTicket.assignee_id,
    reporter_id: dbTicket.reporter_id,
    projectId: dbTicket.project_id,
    createdAt: dbTicket.created_at,
    updatedAt: dbTicket.updated_at || dbTicket.created_at,
    dueDate: dbTicket.due_date || undefined,
    estimatedHours: dbTicket.estimated_hours || undefined,
    labels: (dbTicket.labels as unknown as string[]) || [],
    githubPRs: (dbTicket.github_prs as unknown as GitHubPR[]) || [],
    assignee: dbTicket.assignee
      ? {
          id: dbTicket.assignee.id,
          name: dbTicket.assignee.name,
          email: "",
          role: "developer",
          image_url: "",
        }
      : null,
    comments: [],
    reporter: dbTicket.reporter
      ? {
          id: dbTicket.reporter.id,
          name: dbTicket.reporter.name,
          email: "",
          role: "developer",
          image_url: "",
        }
      : null,
    ...(dbTicket.jira_key && { jiraId: dbTicket.jira_key }),
    position: 0,
    assignees: [],
  };
}

export default useKanbanStore;
