import type { VercelRequest, VercelResponse } from "@vercel/node";
import { syncService } from "../services/SyncService.js";
import logger from "../../utils/logger.js";

export default async function baseWebhook(
  req: VercelRequest,
  res: VercelResponse
) {
  if (req.method !== "POST") {
    res.setHeader("Allow", "POST");
    return res.status(405).send("Method Not Allowed");
  }

  try {
    const event = req.body;
    logger.info(
      `[<PERSON>ra Webhook] Received event: ${event.webhookEvent}`,
      event
    );
 
    await syncService.handleJiraWebhook(event);

    return res.status(200).send("Webhook processed successfully.");
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred.";
    logger.error("[Jira Webhook] Error processing webhook", {
      errorMessage: message,
      errorStack: error instanceof Error ? error.stack : undefined,
      requestBody: req.body,
    });
    return res.status(500).send(`Webhook processing failed: ${message}`);
  }
}
