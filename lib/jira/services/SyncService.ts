/* eslint-disable @typescript-eslint/no-explicit-any */
import type { VercelRequest, VercelResponse } from "@vercel/node";
import type {
  IJiraIssue,
  IJiraWebhookEvent,
  IJiraClient,
  IJiraComment,
  IJiraProject,
  IJiraTransition,
} from "../../types/jira.js";
import {
  IKanbanTask,
  IKanbanComment,
  IKanbanProject,
} from "../../types/kanban.js";
import JiraClient from "../clients/JiraClient.js";
import useKanbanStore from "../../stores/kanban/kanbanStore.js";
import { useProjectStore } from "../../../src/store/projectStore.js";
import {
  mapKanbanTaskToJiraPayload,
  extractTextFromJiraCommentBody,
  mapJiraProjectToKanbanProject,
  mapKanbanStatusToJira,
} from "../utils/mappers.js";
import logger from "../../utils/logger.js";

interface ISyncResult {
  success: boolean;
  action:
    | "create"
    | "update"
    | "delete"
    | "status-change"
    | "comment"
    | "project-create"
    | "comment-update"
    | "comment-delete";
  entityId: string;
  jiraReference?: string;
  error?: string;

  commentId?: string;
}

export class SyncService {
  private jiraClient: IJiraClient;

  constructor(jiraClient: IJiraClient) {
    this.jiraClient = jiraClient;
  }

  public async handleJiraWebhook(
    event: IJiraWebhookEvent
  ): Promise<ISyncResult> {
    // --- Start of Critical Safeguard ---
    // This MUST be the absolute first check before any processing or logging of the event object itself.
    if (!event || typeof event.webhookEvent !== "string") {
      // Log what was actually received if the event structure is not as expected.
      logger.error(
        "[SyncService.handleJiraWebhook] Invalid event object or webhookEvent field.",
        {
          eventReceived: event, // Log the event as is, even if problematic
          webhookEventType: event
            ? typeof event.webhookEvent
            : "undefined (event is null/undefined)",
        }
      );
      // Throwing here ensures the Vercel handler's catch block is hit if this initial validation fails.
      // If the error still says startsWith, it means this block was NOT executed.
      // If this block IS executed, the error message in the logs should be the one below.
      // If the error is still startsWith, the issue is likely between this check and the startsWith line.
      // (Highly unlikely, but we need to rule it out).

      throw new Error(
        "Invalid webhook event: Missing event object or 'webhookEvent' field is not a string."
      );
    }

    // Now that we've validated `event` and `event.webhookEvent`, we can safely log it.
    logger.debug("[SyncService.handleJiraWebhook] Processing valid event:", {
      event,
    });

    try {
      // Log the event object immediately before destructuring
      logger.debug(
        "[SyncService.handleJiraWebhook] Event before destructuring:",
        { event }
      );

      // --- Enhanced Debugging for webhookEvent ---
      const eventObjectForProcessing = event; // Use a clearly named reference
      logger.debug(
        "[SyncService.handleJiraWebhook] Event object reference before direct access:",
        { eventObjectForProcessing }
      );

      const directlyAccessedWebhookEvent =
        eventObjectForProcessing.webhookEvent;
      logger.debug(
        "[SyncService.handleJiraWebhook] 'webhookEvent' accessed directly from event object:",
        {
          value: directlyAccessedWebhookEvent,
          type: typeof directlyAccessedWebhookEvent,
        }
      );

      const { issue, project } = eventObjectForProcessing; // Destructure other properties
      const webhookEvent = directlyAccessedWebhookEvent; // Use the directly accessed value

      logger.debug(
        "[SyncService.handleJiraWebhook] Final 'webhookEvent' variable to be used:",
        { webhookEvent, type: typeof webhookEvent, issue, project }
      );
      // --- End of Enhanced Debugging ---

      // Final check immediately before calling startsWith
      if (typeof webhookEvent !== "string") {
        logger.error(
          "[SyncService.handleJiraWebhook] webhookEvent is not a string immediately before startsWith check.",
          {
            webhookEventValue: webhookEvent,
            webhookEventType: typeof webhookEvent,
          }
        );
        throw new Error(
          "Internal Error: webhookEvent is not a string after validation and destructuring."
        );
      }

      console.log(webhookEvent, "webhookEvent type:", typeof webhookEvent);
      if (
        webhookEvent &&
        webhookEvent.startsWith("jira:issue_") &&
        (!issue || !issue.key)
      ) {
        throw new Error(
          `Missing issue data in ${webhookEvent} webhook payload`
        );
      }

      switch (webhookEvent) {
        case "jira:issue_created":
          if (!issue)
            throw new Error("Issue data missing for jira:issue_created");
          return this.syncJiraIssueToKanban(issue);

        case "jira:issue_updated":
          return this.handleJiraIssueUpdateEvent(event);

        case "jira:issue_deleted":
          if (!issue)
            throw new Error("Issue data missing for jira:issue_deleted");
          return this.handleJiraIssueDeletedEvent(issue);

        case "comment_updated":
          if (!event.comment || !issue)
            throw new Error(
              "Comment or issue data missing for comment_updated"
            );
          return this.handleJiraCommentUpdatedEvent(event);

        case "comment_deleted":
          if (!event.comment || !issue)
            throw new Error(
              "Comment or issue data missing for comment_deleted"
            );
          return this.handleJiraCommentDeletedEvent(event);

        case "project_created":
          if (!project || !project.key) {
            throw new Error(
              "Missing project data in project_created webhook payload"
            );
          }
          return this.syncJiraProjectToKanban(project);

        default:
          logger.warn("Unhandled Jira webhook event type", {
            eventType: webhookEvent,
          });
          return {
            success: false,
            action: "update",
            entityId: issue?.key || project?.key || "unknown",
            error: `Unhandled event type: ${webhookEvent}`,
          };
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Error processing Jira webhook", {
        error: message,
        event,
      });
      return {
        success: false,
        action: "update",
        entityId: event.issue?.key ?? event.project?.key ?? "unknown",
        error: message,
      };
    }
  }

  public async syncJiraIssueToKanban(issue: IJiraIssue): Promise<ISyncResult> {
    try {
      await useKanbanStore.getState().syncFromJira(issue);

      if (issue.fields.comment && issue.fields.comment.comments) {
        for (const jiraComment of issue.fields.comment.comments) {
          await this.syncJiraCommentToKanban(issue.key, jiraComment);
        }
      }

      return {
        success: true,
        action: "update",
        entityId: issue.key,
        jiraReference: issue.key,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to sync Jira issue to Kanban", {
        error: message,
        issueKey: issue.key,
      });
      return {
        success: false,
        action: "update",
        entityId: issue.key,
        error: message,
      };
    }
  }

  private async handleJiraIssueUpdateEvent(
    event: IJiraWebhookEvent
  ): Promise<ISyncResult> {
    const { issue, changelog, comment } = event;
    if (!issue || !issue.key) {
      throw new Error("Missing issue data in JIRA update event");
    }

    try {
      const statusChangeDetail = this.getStatusChange(changelog);
      await this.syncJiraIssueToKanban(issue);

      if (comment) {
        await this.syncJiraCommentToKanban(issue.key, comment);
      }

      return {
        success: true,
        action: statusChangeDetail ? "status-change" : "update",
        entityId: issue.key,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to handle Jira update event", {
        error: message,
        issueKey: issue.key,
        changelog,
      });
      return {
        success: false,
        action: "update",
        entityId: issue.key,
        error: message,
      };
    }
  }

  public async syncJiraCommentToKanban(
    jiraIssueKey: string,
    jiraComment: IJiraComment
  ): Promise<ISyncResult> {
    try {
      const tickets = Array.from(useKanbanStore.getState().tickets.values());
      const targetTicket = tickets.find((t) => t.jiraId === jiraIssueKey);

      if (!targetTicket) {
        logger.warn(
          "Cannot sync JIRA comment, Kanban ticket not found for JIRA key",
          { jiraIssueKey }
        );
        return {
          success: false,
          action: "comment",
          entityId: jiraIssueKey,
          error: `Kanban ticket not found for JIRA key ${jiraIssueKey}`,
        };
      }

      const appCommentData = {
        content: extractTextFromJiraCommentBody(jiraComment.body),
        authorName: jiraComment.author?.displayName || "Unknown Jira User",
        userId: null,
        jiraCommentId: jiraComment.id,
      };

      await useKanbanStore
        .getState()
        .addComment(targetTicket.id, appCommentData);

      return {
        success: true,
        action: "comment",
        entityId: jiraIssueKey,
        jiraReference: jiraComment.id,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to sync Jira comment to Kanban", {
        jiraIssueKey,
        jiraCommentId: jiraComment.id,
        error: message,
      });
      return {
        success: false,
        action: "comment",
        entityId: jiraIssueKey,
        jiraReference: jiraComment.id,
        error: message,
      };
    }
  }

  private async handleJiraCommentUpdatedEvent(
    event: IJiraWebhookEvent
  ): Promise<ISyncResult> {
    const { issue, comment } = event;
    if (!issue || !issue.key || !comment || !comment.id) {
      throw new Error(
        "Missing issue or comment data in JIRA comment_updated event"
      );
    }

    try {
      logger.info("Attempting to sync JIRA comment update to Kanban", {
        jiraIssueKey: issue.key,
        jiraCommentId: comment.id,
      });

      const success = await useKanbanStore
        .getState()
        .updateCommentByJiraId(issue.key, comment.id, {
          content: extractTextFromJiraCommentBody(comment.body),

          authorName: comment.author?.displayName || "Unknown Jira User",
        });

      if (!success) {
        logger.warn(
          "Kanban comment not found or update failed for JIRA comment ID",
          { jiraCommentId: comment.id, jiraIssueKey: issue.key }
        );
      }

      return {
        success: true,
        action: "comment-update",
        entityId: issue.key,
        commentId: comment.id,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to handle Jira comment updated event", {
        error: message,
        jiraIssueKey: issue.key,
        jiraCommentId: comment?.id,
      });
      return {
        success: false,
        action: "comment-update",
        entityId: issue.key,
        commentId: comment?.id,
        error: message,
      };
    }
  }

  private async handleJiraCommentDeletedEvent(
    event: IJiraWebhookEvent
  ): Promise<ISyncResult> {
    const { issue, comment } = event;
    if (!issue || !issue.key || !comment || !comment.id) {
      throw new Error(
        "Missing issue or comment data in JIRA comment_deleted event"
      );
    }

    try {
      logger.info("Attempting to delete JIRA comment from Kanban", {
        jiraIssueKey: issue.key,
        jiraCommentId: comment.id,
      });

      const success = await useKanbanStore
        .getState()
        .deleteCommentByJiraId(issue.key, comment.id);

      if (!success) {
        logger.warn(
          "Kanban comment not found or delete failed for JIRA comment ID",
          { jiraCommentId: comment.id, jiraIssueKey: issue.key }
        );
      }

      return {
        success: true,
        action: "comment-delete",
        entityId: issue.key,
        commentId: comment.id,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to handle Jira comment deleted event", {
        error: message,
        jiraIssueKey: issue.key,
        jiraCommentId: comment?.id,
      });
      return {
        success: false,
        action: "comment-delete",
        entityId: issue.key,
        commentId: comment?.id,
        error: message,
      };
    }
  }

  public async syncKanbanTaskToJira(
    task: IKanbanTask,
    jiraProjectKey: string
  ): Promise<ISyncResult> {
    try {
      const jiraPayload = mapKanbanTaskToJiraPayload(task, jiraProjectKey);
      const syncedJiraIssue = await this.jiraClient.createOrUpdateIssue(
        {
          jiraIssueKey: task.jiraId,
          summary: jiraPayload.fields.summary,
          description: jiraPayload.fields.description,
          projectKey: jiraProjectKey,
        },
        jiraProjectKey,
        jiraPayload.fields.issuetype.name
      );

      if (!task.jiraId && syncedJiraIssue.key) {
        await useKanbanStore.getState().updateTicket(task.id, {
          jiraId: syncedJiraIssue.key,
        });
      }

      return {
        success: true,
        action: task.jiraId ? "update" : "create",
        entityId: task.id,
        jiraReference: syncedJiraIssue.key,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to sync Kanban task to Jira", {
        error: message,
        taskId: task.id,
      });
      return {
        success: false,
        action: task.jiraId ? "update" : "create",
        entityId: task.id,
        error: message,
      };
    }
  }

  public async syncKanbanStatusToJira(
    jiraIssueKey: string,
    newKanbanStatus: string
  ): Promise<ISyncResult> {
    try {
      const targetJiraStatusName = mapKanbanStatusToJira(
        newKanbanStatus as any
      );
      if (!targetJiraStatusName) {
        throw new Error(
          `No JIRA status mapping found for Kanban status: ${newKanbanStatus}`
        );
      }

      const availableTransitions =
        await this.jiraClient.getAvailableTransitions(jiraIssueKey);
      const targetTransition = availableTransitions.find(
        (t: IJiraTransition) => t.to.name.toLowerCase() === targetJiraStatusName.toLowerCase()
      );

      if (!targetTransition) {
        logger.warn("No matching JIRA transition found", {
          jiraIssueKey,
          targetJiraStatusName,
          availableTransitions,
        });
        throw new Error(
          `No JIRA transition found for status: ${targetJiraStatusName}`
        );
      }

      await this.jiraClient.transitionIssue(jiraIssueKey, targetTransition.id);
      return { success: true, action: "status-change", entityId: jiraIssueKey };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to sync Kanban status to Jira", {
        jiraIssueKey,
        newKanbanStatus,
        error: message,
      });
      return {
        success: false,
        action: "status-change",
        entityId: jiraIssueKey,
        error: message,
      };
    }
  }

  public async syncKanbanCommentToJira(
    jiraIssueKey: string,
    kanbanComment: IKanbanComment
  ): Promise<ISyncResult> {
    try {
      const jiraCommentResponse = await this.jiraClient.addComment(
        jiraIssueKey,
        {
          body: kanbanComment.content,
        }
      );
      return {
        success: true,
        action: "comment",
        entityId: jiraIssueKey,
        jiraReference: jiraCommentResponse.id,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to sync Kanban comment to Jira", {
        jiraIssueKey,
        kanbanCommentId: kanbanComment.id,
        error: message,
      });
      return {
        success: false,
        action: "comment",
        entityId: jiraIssueKey,
        error: message,
      };
    }
  }

  private getStatusChange(changelog?: IJiraWebhookEvent["changelog"]) {
    const statusItem = changelog?.items?.find(
      (item) => item.field === "status"
    );
    return statusItem
      ? { from: statusItem.fromString, to: statusItem.toString }
      : null;
  }

  public async syncKanbanProjectToJira(
    project: IKanbanProject
  ): Promise<ISyncResult> {
    try {
      const lead = await this.jiraClient.findUserByEmail(process.env.JIRA_EMAIL!);
      if (!lead) {
        throw new Error(`Could not find JIRA user with email ${process.env.JIRA_EMAIL}`);
      }

      const jiraProjectResponse = await this.jiraClient.createProject({
        key: project.key!.toUpperCase(),
        name: project.name,
        projectTypeKey: "software",
        lead: lead.accountId,
      });
      return {
        success: true,
        action: "create",
        entityId: project.id,
        jiraReference: jiraProjectResponse.key,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to sync Kanban project to Jira", {
        error: message,
        projectId: project.id,
      });
      return {
        success: false,
        action: "create",
        entityId: project.id,
        error: message,
      };
    }
  }

  public async syncJiraProjectToKanban(
    jiraProject: IJiraProject
  ): Promise<ISyncResult> {
    try {
      logger.info("Attempting to sync JIRA project to Kanban", {
        projectKey: jiraProject.key,
        projectName: jiraProject.name,
      });

      const kanbanProjectData = await mapJiraProjectToKanbanProject(
        jiraProject
      );

      const syncedProject = await useProjectStore
        .getState()
        .syncProjectFromJira(kanbanProjectData as any);

      if (!syncedProject) {
        throw new Error(
          "Failed to sync project in the project store (returned null)."
        );
      }

      return {
        success: true,
        action: "project-create",
        entityId: jiraProject.key,
        jiraReference: jiraProject.key,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to sync Jira project to Kanban", {
        error: message,
        projectKey: jiraProject.key,
      });
      return {
        success: false,
        action: "project-create",
        entityId: jiraProject.key,
        error: message,
      };
    }
  }

  private async handleJiraIssueDeletedEvent(
    deletedIssue: IJiraIssue
  ): Promise<ISyncResult> {
    try {
      const jiraIssueKey = deletedIssue.key;
      if (!jiraIssueKey) {
        throw new Error("Jira issue key missing in delete event payload.");
      }

      logger.info("Attempting to delete Kanban task for JIRA issue", {
        jiraIssueKey,
      });

      const success = await useKanbanStore
        .getState()
        .deleteTicketByJiraId(jiraIssueKey);

      if (!success) {
        logger.warn("Kanban ticket not found or already deleted for JIRA key", {
          jiraIssueKey,
        });
      }

      return {
        success: true,
        action: "delete",
        entityId: jiraIssueKey,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error("Failed to handle Jira issue deleted event", {
        error: message,
        jiraIssueKey: deletedIssue.key,
      });
      return {
        success: false,
        action: "delete",
        entityId: deletedIssue.key,
        error: message,
      };
    }
  }
}

export const syncService = new SyncService(new JiraClient());

// Vercel Serverless Function Handler
export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== "POST") {
    res.setHeader("Allow", ["POST"]);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    const eventPayload: IJiraWebhookEvent = req.body; // Vercel parses JSON body by default

    // Basic validation of the payload
    if (!eventPayload || typeof eventPayload.webhookEvent !== "string") {
      logger.error(
        "Invalid webhook payload: webhookEvent missing or not a string.",
        { body: req.body }
      );
      return res.status(400).json({
        success: false,
        error: "Invalid webhook payload: webhookEvent missing or not a string.",
      });
    }
    console.log(eventPayload, "eventPayload type:", typeof eventPayload);
    const result = await syncService.handleJiraWebhook(eventPayload);
    return res.status(result.success ? 200 : 500).json(result);
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error("Unhandled error in Jira SyncService API handler", {
      error: message,
      errorObject: error, // Log the full error object for more details (e.g., stack trace)
    });
    return res.status(500).json({
      success: false,
      error: "Internal Server Error processing webhook.",
    });
  }
}
