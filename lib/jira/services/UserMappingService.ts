import type { IJiraUser } from "../../types/jira.js";
import type { UserProfile } from "../../../src/types/index.js";
import { getSupabaseClient } from "../../../src/integrations/supabase/client.js";
import logger from "../../utils/logger.js";

const supabase = getSupabaseClient(null);

export class UserMappingService {
  /**
   * Finds a Kanban user (UserProfile) by matching the email address from a Jira user.
   * For now, it only finds existing users. Creation of new users is not yet implemented.
   * @param jiraUser The Jira user object.
   * @returns A Promise that resolves to the UserProfile if found, or null otherwise.
   */
  public async findKanbanUserByJiraUser(
    jiraUser: IJiraUser
  ): Promise<UserProfile | null> {
    if (!jiraUser.emailAddress) {
      logger.warn(
        "[UserMappingService] Jira user does not have an email address, cannot map.",
        {
          jiraAccountId: jiraUser.accountId,
          jiraDisplayName: jiraUser.displayName,
        }
      );
      return null;
    }

    try {
      const { data, error } = await supabase
        .from("users") // Assuming your user profiles table is named 'profiles'
        .select("*") // Select all fields to reconstruct UserProfile
        .eq("email", jiraUser.emailAddress)
        .limit(1)
        .single(); // Expects a single row or null

      if (error && error.code !== "PGRST116") {
        // PGRST116: "Searched for a single row, but found no rows" (not an error for us here)
        logger.error(
          "[UserMappingService] Error fetching user from Supabase by email",
          { email: jiraUser.emailAddress, error }
        );
        throw error;
      }

      return data ? (data as UserProfile) : null;
    } catch (error) {
      // Logged above if it's a Supabase query error
      // If it's another type of error, log it here or let it propagate
      return null;
    }
  }
}

export const userMappingService = new UserMappingService();
