import type {
  IJiraClient,
  IJiraProject,
  IJiraIssue,
  IJiraComment,
  IJiraTransition,
  IJiraUser,
} from "../../types/jira";
import logger from "../../utils/logger.js";

export class JiraClient implements IJiraClient {
  private baseUrl: string;
  private jiraEmail: string;
  private jiraApiKey: string;

  constructor() {
    const jiraBaseUrl = process.env.JIRA_BASE_URL;
    const jiraEmail = process.env.JIRA_EMAIL;
    const jiraApiKey = process.env.JIRA_API_KEY;

    if (!jiraBaseUrl) {
      throw new Error("JIRA_BASE_URL environment variable is not set.");
    }
    if (!jiraEmail) {
      throw new Error("JIRA_EMAIL environment variable is not set.");
    }
    if (!jiraApiKey) {
      throw new Error("JIRA_API_KEY environment variable is not set.");
    }

    this.baseUrl = jiraBaseUrl.endsWith('/') ? jiraBaseUrl.slice(0, -1) : jiraBaseUrl;
    this.jiraEmail = jiraEmail;
    this.jiraApiKey = jiraApiKey;
  }

  async addComment(
    issueKey: string,
    comment: { body: string }
  ): Promise<IJiraComment> {
    const adfComment = {
      body: {
        type: "doc",
        version: 1,
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: comment.body }],
          },
        ].filter((c) => c.content.length > 0),
      },
    };
    const response = await fetch(
      `${this.baseUrl}/rest/api/3/issue/${issueKey}/comment`,
      {
        method: "POST",
        headers: this.getAuthHeadersWithContentType(),
        body: JSON.stringify(adfComment),
      }
    );
    return this.handleResponse<IJiraComment>(response);
  }

  async createProject(project: {
    name: string;
    key: string;
    projectTypeKey: string;
    lead: string;
    templateKey?: string;
  }): Promise<{ id: string; key: string }> {
    const response = await fetch(`${this.baseUrl}/rest/api/3/project`, {
      method: "POST",
      headers: this.getAuthHeadersWithContentType(),
      body: JSON.stringify(project),
    });

    return this.handleResponse(response);
  }

  async getProject(projectKey: string): Promise<IJiraProject> {
    const res = await fetch(
      `${this.baseUrl}/rest/api/3/project/${projectKey}`,
      {
        headers: this.getAuthHeaders(),
      }
    );
    return this.handleResponse<IJiraProject>(res);
  }

  async findProjectByName(projectName: string): Promise<IJiraProject | null> {
    // JIRA API doesn't have a direct "find by exact name" search.
    // We can use project search with a query or list projects and filter.
    // Using /rest/api/3/project/search?query=<projectName> is one option.
    // For more precise matching, fetching all visible projects and filtering might be needed for smaller instances.
    // Let's assume a search query will work well enough.
    const res = await fetch(
      `${this.baseUrl}/rest/api/3/project/search?query=${encodeURIComponent(
        projectName
      )}`,
      {
        headers: this.getAuthHeaders(),
      }
    );
    const searchResult = await this.handleResponse<{ values: IJiraProject[] }>(
      res
    );
    // This might return multiple results if projectName is a substring of others.
    // Exact match:
    const foundProject = searchResult.values.find(
      (p) => p.name.toLowerCase() === projectName.toLowerCase()
    );
    return foundProject || null;
  }

  async findUserByEmail(email: string): Promise<IJiraUser | null> {
    const res = await fetch(
      `${this.baseUrl}/rest/api/3/user/search?query=${encodeURIComponent(
        email
      )}`,
      {
        headers: this.getAuthHeaders(),
      }
    );
    const users = await this.handleResponse<IJiraUser[]>(res);
    return users.length > 0 ? users[0] : null;
  }

  async createIssue(issue: {
    fields: {
      project: { key: string };
      summary: string;
      description?: any;
      issuetype: { name: string };
      reporter?: { accountId: string };
      assignee?: { accountId: string };
      priority?: { name: string };
      labels?: string[];
    };
  }): Promise<IJiraIssue> {
    const res = await fetch(`${this.baseUrl}/rest/api/3/issue`, {
      method: "POST",
      headers: this.getAuthHeadersWithContentType(),
      body: JSON.stringify(issue),
    });
    return this.handleResponse<IJiraIssue>(res);
  }

  async updateIssue(
    issueKey: string,
    update: {
      fields?: {
        summary?: string;
        description?: any;
        assignee?: { accountId: string | null };
        reporter?: { accountId: string };
        priority?: { name: string };
        labels?: string[];
        // Note: Status is updated via transitions, not by setting a field here.
      };
    }
  ): Promise<void> {
    const res = await fetch(`${this.baseUrl}/rest/api/3/issue/${issueKey}`, {
      method: "PUT",
      headers: this.getAuthHeadersWithContentType(),
      body: JSON.stringify(update),
    });

    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(
        `Update issue failed: ${res.status} ${res.statusText} - ${errorText}`
      );
    }
    // PUT usually returns 204 No Content on success
  }

  async createOrUpdateIssue(
    appTicket: any, // Replace 'any' with your application's Ticket type
    projectKey: string,
    issueType: string = "Task"
  ): Promise<IJiraIssue> {
    // This method would map your appTicket to JIRA's format
    // For simplicity, assuming appTicket has properties like jiraIssueKey, summary, description

    const descriptionADF = appTicket.description
      ? {
          type: "doc",
          version: 1,
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: appTicket.description }],
            },
          ],
        }
      : undefined;

    if (appTicket.jiraIssueKey) {
      // Update existing issue
      const payload = {
        fields: {
          summary: appTicket.summary,
          description: descriptionADF,
          // Add other mappable fields: priority, assignee, reporter, labels
        },
      };
      await this.updateIssue(appTicket.jiraIssueKey, payload);
      // Re-fetch the issue to get the full updated state as updateIssue returns void
      return this.getIssue(appTicket.jiraIssueKey);
    } else {
      // Create new issue
      const payload = {
        fields: {
          project: { key: projectKey },
          summary: appTicket.summary,
          description: descriptionADF,
          issuetype: { name: issueType },
          // Add other mappable fields
        },
      };
      return this.createIssue(payload);
    }
  }

  async getIssue(issueKey: string, expand?: string[]): Promise<IJiraIssue> {
    let url = `${this.baseUrl}/rest/api/3/issue/${issueKey}`;
    if (expand && expand.length > 0) {
      url += `?expand=${expand.join(",")}`;
    }
    const res = await fetch(url, { headers: this.getAuthHeaders() });
    return this.handleResponse<IJiraIssue>(res);
  }

  async getAvailableTransitions(issueKey: string): Promise<IJiraTransition[]> {
    const res = await fetch(
      `${this.baseUrl}/rest/api/3/issue/${issueKey}/transitions`,
      {
        headers: this.getAuthHeaders(),
      }
    );
    const data = await this.handleResponse<{ transitions: IJiraTransition[] }>(
      res
    );
    return data.transitions;
  }

  async transitionIssue(issueKey: string, transitionId: string): Promise<void> {
    const res = await fetch(
      `${this.baseUrl}/rest/api/3/issue/${issueKey}/transitions`,
      {
        method: "POST",
        headers: this.getAuthHeadersWithContentType(),
        body: JSON.stringify({ transition: { id: transitionId } }),
      }
    );
    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(
        `Transition issue failed: ${res.status} ${res.statusText} - ${errorText}`
      );
    }
    // POST to transitions usually returns 204 No Content
  }

  // Shared Utilities
  private getAuthHeaders() {
    return {
      Authorization: `Basic ${Buffer.from(
        `${this.jiraEmail}:${this.jiraApiKey}`
      ).toString("base64")}`,
      Accept: "application/json",
    };
  }

  public getAuthHeadersWithContentType() {
    return {
      ...this.getAuthHeaders(),
      "Content-Type": "application/json",
    };
  }

  public async handleResponse<T>(res: Response): Promise<T> {
    if (!res.ok) {
      const errorBody = await res.text();
      logger.error({ message: "Jira API Error", status: res.status, statusText: res.statusText, errorBody });
      throw new Error(
        `Jira API error: ${res.status} ${res.statusText} - ${errorBody}`
      );
    }
    return res.status === 204 ? ({} as T) : res.json(); // Handle 204 No Content
  }
}

export default JiraClient;
