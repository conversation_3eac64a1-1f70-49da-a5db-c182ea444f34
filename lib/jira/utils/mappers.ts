import type {
  Ticket,
  TicketStatus,
  TicketPriority,
  TicketType,
} from "../../../src/types/index.js";
import type { IKanbanTask } from "../../types/kanban.js";
import type { IJiraIssue, IJiraProject } from "../../types/jira.js";
import { userMappingService } from "../services/UserMappingService.js";
import { DEFAULT_SYNC_CONFIG } from "../../constants/statusMaps.js";
import logger from "../../utils/logger.js";

// Status Mappings
export function mapJiraStatusToKanban(jiraStatus?: string): TicketStatus {
  if (!jiraStatus) return "backlog"; // Default or use a constant
  const mappedStatus = DEFAULT_SYNC_CONFIG.jiraToKanban.statusMap[jiraStatus];
  if (mappedStatus) {
    // Ensure the mapped status is a valid TicketStatus
    // This might require casting or more robust type checking if maps aren't perfectly aligned
    return mappedStatus.toLowerCase().replace(/\s+/g, "-") as TicketStatus;
  }
  logger.warn(
    `No Kanban status mapping found for JIRA status: ${jiraStatus}. Using lowercase original as fallback.`
  );
  return jiraStatus.toLowerCase().replace(/\s+/g, "-") as TicketStatus; // Fallback
}

export function mapKanbanStatusToJira(kanbanStatus: TicketStatus): string {
  const mappedStatus =
    DEFAULT_SYNC_CONFIG.kanbanToJira.statusMap[
      kanbanStatus as keyof typeof DEFAULT_SYNC_CONFIG.kanbanToJira.statusMap
    ];
  if (mappedStatus) {
    return mappedStatus;
  }
  logger.warn(
    `No JIRA status mapping found for Kanban status: ${kanbanStatus}. Using original.`
  );
  // Fallback: Capitalize first letter of each word, replace hyphens with spaces
  return kanbanStatus
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

// Priority Mappings
export function mapJiraPriorityToKanban(jiraPriority?: string): TicketPriority {
  const priorityMap: Record<string, TicketPriority> = {
    Highest: "urgent",
    High: "high",
    Medium: "medium",
    Low: "low",
    Lowest: "low", // Assuming Lowest maps to low
  };
  return priorityMap[jiraPriority || "Medium"] || "medium";
}

export function mapKanbanPriorityToJira(
  kanbanPriority: TicketPriority
): string {
  const priorityMap: Record<TicketPriority, string> = {
    urgent: "Highest",
    high: "High",
    medium: "Medium",
    low: "Low",
  };
  return priorityMap[kanbanPriority];
}

// Type Mappings
export function mapJiraTypeToKanban(jiraType?: string): TicketType {
  const typeMap: Record<string, TicketType> = {
    Bug: "bug",
    Story: "feature",
    Feature: "feature",
    Task: "task",
    Improvement: "improvement",
  };
  return typeMap[jiraType || "Task"] || "task";
}

export function mapKanbanTypeToJira(kanbanType: TicketType): string {
  const typeMap: Record<TicketType, string> = {
    bug: "Bug",
    feature: "Story", // Mapping feature to Story
    task: "Task",
    improvement: "Improvement",
  };
  return typeMap[kanbanType];
}

// Kanban Task to JIRA Payload
export const mapKanbanTaskToJiraPayload = (
  task: IKanbanTask | Ticket,
  jiraProjectKey: string
) => {
  const descriptionADF = task.description
    ? {
        type: "doc",
        version: 1,
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: task.description }],
          },
        ],
      }
    : undefined;
  return {
    fields: {
      project: { key: jiraProjectKey },
      summary: task.title,
      description: descriptionADF,
      issuetype: { name: mapKanbanTypeToJira(task.type as TicketType) },
      priority: {
        name: mapKanbanPriorityToJira(task.priority as TicketPriority),
      },
      labels: task.labels || [],
      // TODO: Map other fields like assignee, reporter from IKanbanTask/Ticket if they exist and are needed
    },
  };
};

// JIRA Comment Body to Text
export function extractTextFromJiraCommentBody(body: any): string {
  if (typeof body === "string") return body;
  // Basic Atlassian Document Format (ADF) text extraction
  if (body && body.type === "doc" && body.content) {
    return body.content
      .map(
        (node: any) =>
          node.content?.map((innerNode: any) => innerNode.text).join("") || ""
      )
      .join("\n");
  }
  return "";
}

// JIRA Project to Kanban Project
// Define the expected structure for the data returned by this mapper,
// which will be used by projectStore.syncProjectFromJira
interface MappedJiraProjectData {
  name: string;
  key: string;
  description?: string;
  jiraProjectId?: string;
  leadId?: string | null;
}

export async function mapJiraProjectToKanbanProject(
  jiraProject: IJiraProject
): Promise<MappedJiraProjectData> {
  const kanbanLeadId = jiraProject.lead
    ? (await userMappingService.findKanbanUserByJiraUser(jiraProject.lead))
        ?.id || null
    : null;
  return {
    name: jiraProject.name,
    description: jiraProject.description || "",
    key: jiraProject.key,
    leadId: kanbanLeadId,
    jiraProjectId: jiraProject.id,
  };
}
