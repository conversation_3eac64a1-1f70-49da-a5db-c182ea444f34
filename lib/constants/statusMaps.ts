import type { ISyncConfig } from '../types/sync.js';

export const DEFAULT_SYNC_CONFIG: ISyncConfig = {
  jiraToKanban: {
    statusMap: {
      'To Do': 'todo',
      'In Progress': 'in-progress',
      'Done': 'done',
      'Backlog': 'backlog',
    },
    fieldMap: {
      summary: 'title',
      description: 'description',
      issuetype: 'type',
      priority: 'priority',
    },
  },
  kanbanToJira: {
    statusMap: {
      backlog: 'Backlog',
      todo: 'To Do',
      'in-progress': 'In Progress',
      review: 'In Review',
      done: 'Done',
    },
  },
};