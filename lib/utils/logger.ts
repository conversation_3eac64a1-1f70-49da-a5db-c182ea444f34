// c:\Users\<USER>\Desktop\kanban\lib\utils\logger.ts

// Define a simple interface for the logger methods we use
interface UniversalLogger {
  info: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  error: (...args: any[]) => void;
  debug: (...args: any[]) => void;
}

let logger: UniversalLogger; // Declare logger using let outside the blocks

if (typeof process !== "undefined" && process.env.NODE_ENV === "test") {
  logger = {
    // Assign to the let variable
    info: () => {},
    warn: () => {},
    error: () => {},
    debug: () => {},
  };
} else {
  logger = {
    // Assign to the let variable
    info: console.info,
    warn: console.warn,
    error: console.error,
    debug: console.debug,
  };
}

export default logger;
