name: Supabase Deployment

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  supabase:
    uses: sparkstrand/github/.github/workflows/supabase-deployment.yml@main
    with:
      supabase_workdir: "."
      package_manager: "npm"
    secrets:
      SUPABASE_ACCESS_TOKEN_DEV: ${{ secrets.SUPABASE_ACCESS_TOKEN_DEV }}
      SUPABASE_PROJECT_REF_DEV: ${{ secrets.SUPABASE_PROJECT_REF_DEV }}
      SUPABASE_ACCESS_TOKEN_PROD: ${{ secrets.SUPABASE_ACCESS_TOKEN_PROD }}
      SUPABASE_PROJECT_REF_PROD: ${{ secrets.SUPABASE_PROJECT_REF_PROD }}