name: <PERSON><PERSON> Vercel Deploy

on:
  push:
    branches: [ "main" ]
  workflow_dispatch:  # Allow manual triggering

jobs:
  Check-Migrations:
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - uses: actions/checkout@v2
      - name: Install Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install Dependencies
        run: npm install
      - name: Link Supabase project
        run: npx supabase link --project-ref ${{ secrets.SUPABASE_PROD_PROJECT_ID }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      - name: Check for missing migrations
        id: check-migrations
        continue-on-error: true
        run: |
          # Get list of migrations from remote
          MIGRATIONS=$(npx supabase migration list --linked --json | jq -r '.[] | .version' || echo "[]")
          
          # Check if any migrations are missing locally
          for version in $MIGRATIONS; do
            if [ ! -f "supabase/migrations/${version}*" ]; then
              echo "::warning::Missing migration file for version $version"
              echo "missing_migrations=true" >> $GITHUB_OUTPUT
            fi
          done
          
          if [ -z "$MIGRATIONS" ]; then
            echo "No migrations found in remote database"
            echo "missing_migrations=false" >> $GITHUB_OUTPUT
          fi

  # Migrate-Database:
  #   needs: Check-Migrations
  #   if: ${{ needs.Check-Migrations.outputs.missing_migrations != 'true' }}
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v2
  #     - name: Install Node.js
  #       uses: actions/setup-node@v2
  #       with:
  #         node-version: '18'
  #     - name: Install Dependencies
  #       run: npm install
  #     - name: Link Supabase project
  #       run: npx supabase link --project-ref ${{ secrets.SUPABASE_PROD_PROJECT_ID }}
  #       env:
  #         SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  #     - name: Run Migrations
  #       run: |
  #         # Run migrations with force flag to bypass version checks
  #         npx supabase db push --password ${{ secrets.SUPABASE_DB_PASSWORD }}

  trigger-vercel-deploy:
    needs: Check-Migrations
    uses: sparkstrand/github/.github/workflows/trigger-vercel-deploy.yml@main
    secrets: inherit

  handle-migration-failure:
    needs: Check-Migrations
    if: ${{ needs.Check-Migrations.outputs.missing_migrations == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Notify team about migration issue
        run: |
          echo "::error::Migration check failed. Please check for missing migration files."
          echo "##[error]Missing migration files detected. Please sync with your team to get the latest migrations."
          exit 1
