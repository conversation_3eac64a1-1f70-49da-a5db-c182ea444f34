name: Preview Deployment (GitHub Actions)

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  pull_request:
    branches-ignore:
      - main
  push:
    branches-ignore:
      - main

jobs:
  Run-Tests:
    uses: sparkstrand/github/.github/workflows/run-tests.yaml@main
    secrets: inherit

  # Migrate-Database:
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v2
  #     - name: Install Node.js
  #       uses: actions/setup-node@v2
  #       with:
  #         node-version: '18'
  #     - name: Install Dependencies
  #       run: npm install
  #     - name: Link Supabase project
  #       run: npx supabase link --project-ref ${{ secrets.SUPABASE_PROJECT_ID }}
  #       env:
  #         SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  #     - name: Run Migrations
  #       run: npx supabase db push --password ${{ secrets.SUPABASE_DB_PASSWORD }}

  Vercel-Deploy:
    needs: [Run-Tests]
    uses: sparkstrand/github/.github/workflows/preview-deployment.yaml@main
    secrets: inherit
