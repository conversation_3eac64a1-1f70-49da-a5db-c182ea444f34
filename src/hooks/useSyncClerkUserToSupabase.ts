
import { useEffect } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { getSupabaseClient } from '@/integrations/supabase/client';

/**
 * Generates a UUID using the browser's crypto API
 */
function generateUUID() {
  // Use the browser crypto API for a real UUID
  return crypto.randomUUID();
}

/**
 * Syncs the Clerk user to the Supabase users table on sign in/up.
 * - Checks if the user exists by clerk_id or email.
 * - If not, inserts the user with required fields.
 */
export function useSyncClerkUserToSupabase() {
  const { user } = useUser();
  const { getToken } = useAuth();

  useEffect(() => {
    if (!user) return;

    const syncUser = async () => {
      const token = await getToken();
      if (!token || typeof token !== 'string' || token.split('.').length !== 3) {
        throw new Error('Invalid Clerk JWT: did not receive a valid 3-part token');
      }
      const supabase = getSupabaseClient(token);

      const clerkId = user.id;
      const email = (user.primaryEmailAddress?.emailAddress || '').toLowerCase();
      const name = user.fullName || user.username || email;
      const image_url = user.imageUrl || null;

      // Check if user exists by clerk_id or email
      // NOTE: The Supabase 'users' table expects 'id' to be a UUID, so we must not use clerkId in any filter on 'id'.
      let existingUser;
      
      // First, try to find by clerk_id
      let response = await supabase.from('users').select('id').eq('clerk_id', clerkId).maybeSingle();
      if (response.error) {
        console.error('Error checking user in Supabase:', response.error);
      } else if (response.data) {
        existingUser = response.data;
      }
      
      // If not found by clerk_id, try by email
      if (!existingUser) {
        response = await supabase.from('users').select('id').eq('email', email).maybeSingle();
        if (response.error) {
          console.error('Error checking user by email in Supabase:', response.error);
        } else if (response.data) {
          existingUser = response.data;
        }
      }
      
      // Only generate a new UUID if inserting a new user
      const userId = existingUser?.id || generateUUID();
      
      // Determine role: preserve existing if present, otherwise default to 'developer'
      let finalRole = 'developer';
      if (existingUser) {
        // Fetch the current role from the DB for this user
        const roleRes = await supabase.from('users').select('role').eq('id', userId).maybeSingle();
        if (roleRes.data && roleRes.data.role) {
          finalRole = roleRes.data.role;
        }
      }
      
      // Use upsert to avoid race conditions and duplicate errors
      const { error: upsertError } = await supabase.from('users').upsert([
        {
          id: userId, // always a valid UUID
          clerk_id: clerkId,
          name,
          email,
          image_url: image_url,
          role: finalRole,
        },
      ], { onConflict: 'email' });
      
      if (upsertError) {
        console.error('Error upserting user into Supabase:', upsertError);
      }
    };

    syncUser();
    // Run only when Clerk user changes
  }, [user, getToken]);
}
