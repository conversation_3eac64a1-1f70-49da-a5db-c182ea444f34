import { getSupabaseClient } from "@/integrations/supabase/client";
import { useAuth } from "@clerk/clerk-react";
import { useCallback } from "react";

export const useSupabase = () => {
  const { getToken } = useAuth();

  const getFreshSupabaseClient = useCallback(async () => {
    const token = await getToken({ template: "supabase" });

    if (!token) {
      throw new Error("Failed to get authentication token.");
    }

    return getSupabaseClient(token);
  }, [getToken]);

  return { getFreshSupabaseClient };
};
