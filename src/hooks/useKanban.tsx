import { useState } from "react";

export interface Ticket {
  id: string;
  title: string;
  description: string;
  assignee?: string;
  priority: "low" | "medium" | "high";
  labels: string[];
  createdAt: Date;
  jiraId?: string;
  githubPrs?: string[];
}

interface Column {
  id: string;
  title: string;
  tickets: Ticket[];
}

export const useKanban = (initialColumns?: Column[]) => {
  const defaultColumns: Column[] = [
    { id: "backlog", title: "Backlog", tickets: [] },
    { id: "todo", title: "To Do", tickets: [] },
    { id: "in-progress", title: "In Progress", tickets: [] },
    { id: "review", title: "In Review", tickets: [] },
    { id: "done", title: "Done", tickets: [] },
  ];

  const [columns, setColumns] = useState<Column[]>(
    initialColumns || defaultColumns
  );

  const addTicket = (columnId: string, ticket: Ticket) => {
    setColumns((prevColumns) =>
      prevColumns.map((column) =>
        column.id === columnId
          ? { ...column, tickets: [...column.tickets, ticket] }
          : column
      )
    );
  };

  const moveTicket = (
    ticketId: string,
    sourceColumnId: string,
    destinationColumnId: string
  ) => {
    setColumns((prevColumns) => {
      const sourceColumn = prevColumns.find((col) => col.id === sourceColumnId);
      const destColumn = prevColumns.find(
        (col) => col.id === destinationColumnId
      );

      if (!sourceColumn || !destColumn) return prevColumns;

      const ticketIndex = sourceColumn.tickets.findIndex(
        (t) => t.id === ticketId
      );
      if (ticketIndex < 0) return prevColumns;

      const ticket = sourceColumn.tickets[ticketIndex];

      const newSourceTickets = [...sourceColumn.tickets];
      newSourceTickets.splice(ticketIndex, 1);

      const newDestTickets = [...destColumn.tickets, ticket];

      return prevColumns.map((column) => {
        if (column.id === sourceColumnId) {
          return { ...column, tickets: newSourceTickets };
        }
        if (column.id === destinationColumnId) {
          return { ...column, tickets: newDestTickets };
        }
        return column;
      });
    });
  };

  const updateTicket = (columnId: string, updatedTicket: Ticket) => {
    setColumns((prevColumns) =>
      prevColumns.map((column) => {
        if (column.id === columnId) {
          return {
            ...column,
            tickets: column.tickets.map((ticket) =>
              ticket.id === updatedTicket.id ? updatedTicket : ticket
            ),
          };
        }
        return column;
      })
    );
  };

  const deleteTicket = (columnId: string, ticketId: string) => {
    setColumns((prevColumns) =>
      prevColumns.map((column) => {
        if (column.id === columnId) {
          return {
            ...column,
            tickets: column.tickets.filter((ticket) => ticket.id !== ticketId),
          };
        }
        return column;
      })
    );
  };

  return {
    columns,
    setColumns,
    addTicket,
    moveTicket,
    updateTicket,
    deleteTicket,
  };
};
