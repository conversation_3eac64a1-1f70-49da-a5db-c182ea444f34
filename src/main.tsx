
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/clerk-react";
import App from './App.tsx';
import './index.css';

// Use publishable key from env (VITE_CLERK_PUBLISHABLE_KEY) or hardcoded test key
// This is acceptable for front-end publishable keys, but never for secret keys
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY || "pk_test_c3VwZXJiLXdvcm0tNy5jbGVyay5hY2NvdW50cy5kZXYk";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ClerkProvider publishableKey={PUBLISHABLE_KEY}>
      <App />
    </ClerkProvider>
  </StrictMode>
);
