import { create } from "zustand";
import type { SupabaseClient } from "@supabase/supabase-js";
import { Ticket, TicketStatus, UserProfile } from "../types";
import {
  mapDbTicketToTicket,
  mapTicketToDbTicket,
  DbTicketWithProfiles,
} from "../lib/dbAdapters";
import { getSupabaseClient } from "@/integrations/supabase/client";

interface Comment {
  id: string;
  ticketId: string;
  userId: string;
  content: string;
  created_at: string;
  updated_at: string;
  user?: UserProfile;
}

interface TicketState {
  tickets: Ticket[];
  isLoading: boolean;
  error: string | null;
  currentTicket: Ticket | null;
  comments: Comment[];

  fetchTickets: (
    projectId?: string,
    supabaseClient?: SupabaseClient
  ) => Promise<void>;
  createTicket: (
    ticket: Omit<
      Ticket,
      "id" | "created_at" | "updated_at" | "githubPRs" | "referenceKey"
    > & { assigneeIds?: string[] },
    supabaseClient?: SupabaseClient
  ) => Promise<Ticket | null>;
  updateTicket: (
    ticketId: string,
    updates: Partial<Ticket> & { assigneeIds?: string[] },
    supabaseClient?: SupabaseClient
  ) => Promise<void>;
  deleteTicket: (id: string, supabaseClient?: SupabaseClient) => Promise<void>;
  setCurrentTicket: (id: string) => void;
  updateTicketStatus: (
    id: string,
    status: TicketStatus,
    supabaseClient?: SupabaseClient
  ) => Promise<void>;
  reorderTicketsInColumn: (columnId: string, newTicketIds: string[]) => void;
  updateTicketsOrder: (
    orderedTickets: { id: string; position: number }[],
    supabase: SupabaseClient
  ) => Promise<void>;
  fetchComments: (
    ticketId: string,
    supabaseClient?: SupabaseClient
  ) => Promise<void>;
  createComment: (
    ticketId: string,
    userId: string,
    content: string,
    supabaseClient?: SupabaseClient
  ) => Promise<void>;
}

export const useTicketStore = create<TicketState>((set, get) => ({
  tickets: [],
  comments: [],
  isLoading: false,
  error: null,
  currentTicket: null,

  setCurrentTicket: (id) => {
    const ticket = get().tickets.find((t) => t.id === id) || null;
    set({ currentTicket: ticket });
    if (!ticket) set({ comments: [] });
  },

  fetchTickets: async (projectId, supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true, error: null });

    try {
      let query = supabase.from("tickets").select(`
      *,
      reporter:users!reporter_id (id, name, email, image_url, role),
      ticket_assignees (
        user:users!ticket_assignees_user_id_fkey (id, name, email, image_url, role)
      )
    `);

      if (projectId) {
        query = query.eq("project_id", projectId);
      }

      const { data, error } = await query.order("created_at", {
        ascending: false,
      });
      if (error) throw error;

      const processedTickets =
        data?.map((ticket: any) => ({
          ...ticket,
          assignees: ticket.ticket_assignees.map((ta: any) => ta.user),
          ticket_assignees: undefined,
          comments: [],
          githubPRs: ticket.githubPRs || [],
          labels: ticket.labels || [],
        })) || [];

      set({ tickets: processedTickets, isLoading: false });
    } catch (e: any) {
      console.error("Error fetching tickets:", e);
      set({ error: e.message, isLoading: false });
    }
  },

  createTicket: async (ticketData, supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true });

    // Extract project ID from either projectId or project_id field
    const { assigneeIds, projectId, project_id, ...rest } = ticketData;
    const finalProjectId = project_id || projectId;

    if (!finalProjectId) {
      throw new Error("Project ID is required to create a ticket");
    }

    // Create a clean object without projectId to avoid conflicts
    const cleanRest = Object.fromEntries(
      Object.entries(rest).filter(([key]) => key !== "projectId")
    ) as Omit<typeof rest, "projectId">;

    try {
      const { data: newTicket, error } = await supabase
        .from("tickets")
        .insert({
          ...cleanRest,
          project_id: finalProjectId,
          labels: cleanRest.labels || [],
        })
        .select(
          `
          *,
          reporter:users!reporter_id (id, name, email, image_url, role)
        `
        )

        .single();

      if (error) throw error;
      if (!newTicket) throw new Error("Ticket creation failed.");

      if (assigneeIds && assigneeIds.length > 0) {
        const assignments = assigneeIds.map((userId) => ({
          ticket_id: newTicket.id,
          user_id: userId,
          assigned_at: new Date().toISOString(),
        }));
        const { error: assignmentError } = await supabase
          .from("ticket_assignees")
          .insert(assignments);
        if (assignmentError) throw assignmentError;
      }

      await get().fetchTickets(newTicket.project_id, supabase);
      set({ isLoading: false });
      return newTicket as Ticket;
    } catch (e: any) {
      console.error("Error creating ticket:", e);
      set({ error: e.message, isLoading: false });
      return null;
    }
  },

  updateTicket: async (ticketId, updates, supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true });

    const { assigneeIds, ...fieldsToUpdate } = updates || {};

    try {
      if (Object.keys(fieldsToUpdate).length > 0) {
        const { error } = await supabase
          .from("tickets")
          .update(fieldsToUpdate)
          .eq("id", ticketId);
        if (error) throw error;
      }

      if (assigneeIds !== undefined) {
        const { error: deleteError } = await supabase
          .from("ticket_assignees")
          .delete()
          .eq("ticket_id", ticketId);
        if (deleteError) throw deleteError;

        if (assigneeIds.length > 0) {
          const newAssignments = assigneeIds.map((userId) => ({
            ticket_id: ticketId,
            user_id: userId,
            assigned_at: new Date().toISOString(),
          }));
          const { error: insertError } = await supabase
            .from("ticket_assignees")
            .insert(newAssignments);
          if (insertError) throw insertError;
        }
      }

      const currentProjectId = get().tickets.find(
        (t) => t.id === ticketId
      )?.project_id;
      if (currentProjectId) {
        await get().fetchTickets(currentProjectId, supabase);
      }

      set({ isLoading: false });
    } catch (e: any) {
      console.error("Error updating ticket:", e);
      set({ error: e.message, isLoading: false });
    }
  },

  deleteTicket: async (id, supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true, error: null });

    try {
      const { error } = await supabase.from("tickets").delete().eq("id", id);
      if (error) throw error;

      set((state) => ({
        isLoading: false,
        tickets: state.tickets.filter((t) => t.id !== id),
        currentTicket:
          state.currentTicket?.id === id ? null : state.currentTicket,
      }));
    } catch (e: any) {
      console.error("Error deleting ticket:", e);
      set({ error: e.message, isLoading: false });
    }
  },

  updateTicketStatus: async (
    id: string,
    status: TicketStatus,
    supabaseClient: SupabaseClient
  ) => {
    set({ isLoading: true });

    try {
      const { data, error } = await supabaseClient
        .from("tickets")
        .update({ status })
        .eq("id", id)
        .select("id, status")
        .single();

      if (error) {
        throw error;
      }

      if (data) {
        set((state) => ({
          isLoading: false,

          tickets: state.tickets.map((t) =>
            t.id === id ? { ...t, status: data.status as TicketStatus } : t
          ),

          currentTicket:
            state.currentTicket?.id === id
              ? { ...state.currentTicket, status: data.status as TicketStatus }
              : state.currentTicket,
        }));
      } else {
        set({ isLoading: false });
      }
    } catch (e: any) {
      console.error("Error updating ticket status in the store:", e);
      set({ error: e.message, isLoading: false });

      throw e;
    }
  },

  fetchComments: async (ticketId, supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from("comments")
        .select(`*, user:users!user_id (*)`)
        .eq("ticket_id", ticketId)
        .order("created_at", { ascending: true });

      if (error) throw error;
      set({ comments: data as Comment[], isLoading: false });
    } catch (e: any) {
      console.error("Error fetching comments:", e);
      set({ error: e.message, isLoading: false });
    }
  },

  createComment: async (ticketId, userId, content, supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);

    try {
      const { error } = await supabase.from("comments").insert({
        ticket_id: ticketId,
        user_id: userId,
        content,
      });

      if (error) throw error;

      await get().fetchComments(ticketId, supabase);
      set({ isLoading: false });
    } catch (e: any) {
      console.error("Error creating comment:", e);
      set({ error: e.message, isLoading: false });
    }
  },

  reorderTicketsInColumn: (columnId: string, newTicketIds: string[]) => {
    console.log(`Reordering tickets in column ${columnId}:`, newTicketIds);
    set((state) => {
      const otherColumnTickets = state.tickets.filter(
        (ticket) => ticket.status !== columnId
      );

      const reorderedColumnTickets = newTicketIds
        .map((id, index) => {
          const ticket = state.tickets.find((ticket) => ticket.id === id);
          return ticket ? { ...ticket, position: index } : null;
        })
        .filter((ticket): ticket is Ticket => !!ticket);

      console.log("New ticket order with positions:", reorderedColumnTickets);
      return {
        tickets: [...otherColumnTickets, ...reorderedColumnTickets],
      };
    });
  },

  updateTicketsOrder: async (orderedTickets, supabaseClient) => {
    console.log("Updating ticket order in DB:", orderedTickets);
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true, error: null });

    try {
      const validUpdates = orderedTickets.filter(
        (ticket) => ticket.id && /^[\da-f-]{36}$/i.test(ticket.id)
      );

      const updates = validUpdates.map((ticket) =>
        supabase
          .from("tickets")
          .update({ position: ticket.position })
          .eq("id", ticket.id)
      );

      const results = await Promise.all(updates);
      const errors = results.filter((result) => result.error);

      if (errors.length > 0) {
        throw new Error("Some ticket updates failed");
      }

      set({ isLoading: false });
    } catch (e: any) {
      console.error("Error updating ticket order:", e);
      set({ error: e.message, isLoading: false });

      const { toast } = await import("@/components/ui/use-toast");
      toast({
        title: "Error",
        description: "Failed to save ticket order. Please try again.",
        variant: "destructive",
      });
      throw e;
    }
  },
}));
