import { create } from "zustand";
import type { SupabaseClient } from "@supabase/supabase-js";
import { Project } from "@/types";
import { getSupabaseClient } from "@/integrations/supabase/client";
import {
  syncMissingProjectKeysFromJira,
  lookupJiraKeyByName,
} from "@/lib/jiraSync";
import { syncService } from "@lib/jira/services/SyncService";
import logger from "@lib/utils/logger";
import { IKanbanProject } from "@lib/types";
import { UserRole } from "@/types/index";

interface ProjectMember {
  id: string;
  name: string;
  email: string;
  role: string;
}

interface ProjectStore {
  projects: Project[];
  isLoading: boolean;
  error: string | null;
  currentProject: Project | null;
  fetchProjects: (supabase?: SupabaseClient) => Promise<Project[]>;
  createProject: (
    projectData: Omit<Project, "id" | "createdAt" | "updatedAt"> & {
      syncWithJira?: boolean;
    },
    supabase: SupabaseClient,
    creatorDbUuid: string
  ) => Promise<void>;
  updateProject: (
    updates: Partial<Project>,
    supabase: SupabaseClient
  ) => Promise<void>;
  deleteProject: (projectId: string, supabase: SupabaseClient) => Promise<void>;
  addProjectMember: (
    projectId: string,
    userId: string,
    employeeId: string | null,
    role: string,
    supabase: SupabaseClient
  ) => Promise<void>;
  removeProjectMember: (
    projectId: string,
    userId: string,
    supabase: SupabaseClient
  ) => Promise<void>;

  syncProjectFromJira: (projectData: {
    name: string;
    key: string;
    description?: string;
    jiraProjectId?: string;
    leadId?: string | null;
  }) => Promise<Project | null>;
  updateProjectMemberRole: (
    projectId: string,
    userId: string,
    newRole: string,
    supabase: SupabaseClient
  ) => Promise<void>;
  setCurrentProject: (project: Project | null) => void;
}

export const useProjectStore = create<ProjectStore>((set, get) => ({
  projects: [],
  isLoading: false,
  error: null,
  currentProject: null,

  fetchProjects: async (supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true });
    try {
      const { data, error } = await supabase
        .from("projects")
        .select("*, members:project_members(user_id, role, created_at, users(id, name, email, image_url, role, clerk_id))")
        .order("created_at", { ascending: false });

      if (error) throw error;

      const projectsWithMembers = data.map((project: any) => ({
        id: project.id,
        name: project.name,
        description: project.description ?? "",
        key: project.key ?? "",
        ownerId: project.owner_id ?? "",
        createdAt: project.created_at ?? "",
        updatedAt: project.updated_at ?? "",
        jiraProjectId: project.jira_project_id ?? "",
        slackChannelId: project.slack_channel_id ?? "",
        members: (project.members || []).map((pm: any) => ({
          id: pm.user_id,
          user_id: pm.user_id,
          project_id: project.id,
          role: pm.role,
          created_at: pm.created_at || "",
          user: pm.users ? {
            id: pm.users.id,
            name: pm.users.name,
            email: pm.users.email,
            role: pm.role,
            image_url: pm.users.image_url || "",
            clerk_id: pm.users.clerk_id || ""
          } : undefined
        })),
      }));

      set({ isLoading: false, projects: projectsWithMembers });
      return projectsWithMembers;
    } catch (error) {
      console.error("Error fetching projects:", error);
      set({ isLoading: false, error: "Failed to fetch projects" });
      return [];
    }
  },

  createProject: async (projectData, supabase, creatorDbUuid) => {
    set({ isLoading: true });

    try {
      const { data, error } = await supabase
        .from("projects")
        .insert({
          name: projectData.name,
          description: projectData.description ?? "",
          key: projectData.key?.toUpperCase() || "",
          status: "active",
          start_date: new Date().toISOString(),
          end_date: null,
          project_type: "INTERNAL",
          client_name: null,
          github_repo: null,
        })
        .select("*")
        .single();

      if (error) throw error;

      const { error: memberError } = await supabase
        .from("project_members")
        .insert([
          {
            project_id: data.id,
            user_id: creatorDbUuid,
            role: "admin",
          },
        ]);

      if (memberError) throw memberError;

      const newProject: Project = {
        id: data.id,
        name: data.name,
        key: data.key,
        description: data.description,
        ownerId: data.ownerId,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        members: [],
        jiraProjectId: data.jiraProjectId,
        slackChannelId: data.slackChannelId,
      };
      set((state) => ({
        projects: [...state.projects, newProject],
        isLoading: false,
      }));
    } catch (error) {
      console.error("Error creating project:", error);
      set({ isLoading: false });
      throw error;
    }
  },

  updateProject: async (updates, supabase) => {
    set({ isLoading: true });
    try {
      const { data, error } = await supabase
        .from("projects")
        .update(updates)
        .eq("id", updates.id)
        .select("*")
        .single();

      if (error) throw error;

      set((state) => ({
        projects: state.projects.map((p) =>
          p.id === data.id ? { ...p, ...data } : p
        ),
        currentProject:
          state.currentProject?.id === data.id
            ? { ...state.currentProject, ...data }
            : state.currentProject,
        isLoading: false,
      }));
    } catch (error) {
      console.error("Error updating project:", error);
      set({ isLoading: false });
      throw error;
    }
  },

  deleteProject: async (projectId, supabase) => {
    set({ isLoading: true });
    try {
      // First, delete all tickets associated with the project
      const { error: ticketsError } = await supabase
        .from("tickets")
        .delete()
        .eq("project_id", projectId);

      if (ticketsError) throw ticketsError;

      // Then, delete the project itself
      const { error: projectError } = await supabase
        .from("projects")
        .delete()
        .eq("id", projectId);

      if (projectError) throw projectError;

      set((state) => ({
        projects: state.projects.filter((p) => p.id !== projectId),
        currentProject:
          state.currentProject?.id === projectId ? null : state.currentProject,
        isLoading: false,
      }));
    } catch (error) {
      console.error("Error deleting project:", error);
      set({ isLoading: false });
      throw error;
    }
  },

  addProjectMember: async (
    projectId,
    userId,
    employeeId,
    role,
    supabase
  ) => {
    set({ isLoading: true });
    try {
      const memberData: {
        project_id: string;
        user_id: string;
        role: string;
        employee_id?: string;
      } = {
        project_id: projectId,
        user_id: userId,
        role,
      };

      if (employeeId) {
        memberData.employee_id = employeeId;
      }

      const { error } = await supabase
        .from("project_members")
        .insert([memberData]);

      if (error) throw error;

      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("id, name, email")
        .eq("id", userId)
        .single();

      if (userError) throw userError;

      set((state) => {
        const updatedCurrentProject = state.currentProject
          ? {
              ...state.currentProject,
              members: [
                ...(state.currentProject.members || []),
                userData.id,
              ],
            }
          : null;

        return {
          currentProject: updatedCurrentProject,
          isLoading: false,
          projects: state.projects,
        };
      });
    } catch (error) {
      console.error("Error adding project member:", error);
      set({ isLoading: false });
      throw error;
    }
  },

  removeProjectMember: async (projectId, userId, supabase) => {
    set({ isLoading: true });
    try {
      const { error } = await supabase
        .from("project_members")
        .delete()
        .eq("project_id", projectId)
        .eq("user_id", userId);

      if (error) throw error;

      set((state) => {
        const updatedCurrentProject = state.currentProject
          ? {
              ...state.currentProject,
              members: state.currentProject.members.filter(
                (m) => m.id !== userId
              ),
            }
          : null;

        return {
          currentProject: updatedCurrentProject,
          isLoading: false,
          projects: state.projects,
        };
      });
    } catch (error) {
      console.error("Error removing project member:", error);
      set({ isLoading: false });
      throw error;
    }
  },

  setCurrentProject: (project) => set({ currentProject: project }),

  updateProjectMemberRole: async (projectId, userId, newRole, supabase) => {
    set({ isLoading: true });
    try {
      const { error } = await supabase
        .from("project_members")
        .update({ role: newRole })
        .eq("project_id", projectId)
        .eq("user_id", userId);

      if (error) throw error;

      set((state) => {
        const updatedCurrentProject = state.currentProject
          ? {
              ...state.currentProject,
              members: state.currentProject.members.map((m) => {
                if (m.id === userId) {
                  return { ...m, role: newRole as UserRole };
                }
                return m;
              }),
            }
          : null;

        return {
          currentProject: updatedCurrentProject,
          isLoading: false,
          projects: state.projects,
        };
      });
    } catch (error) {
      console.error("Error updating member role:", error);
      set({ isLoading: false });
      throw error;
    }
  },

  syncProjectFromJira: async (projectData) => {
    const supabase = getSupabaseClient(null);
    set({ isLoading: true });
    try {
      const { data, error } = await supabase
        .from("projects")
        .update({
          key: projectData.key,
          description: projectData.description,
        })
        .eq("name", projectData.name)
        .select("*")
        .single();

      if (error) throw error;
      if (!data) return null;

      const project: Project = {
        id: data.id,
        name: data.name,
        description: data.description ?? "",
        key: data.key ?? "",
        ownerId: data.owner_id ?? "",
        createdAt: data.created_at ?? "",
        updatedAt: data.updated_at ?? "",
        jira_project_id: (data as any).jira_project_id ?? "",
        slackChannelId: (data as any).slack_channel_id ?? "",
        members: [],
      };

      set((state) => ({
        projects: state.projects.map((p) =>
          p.id === project.id ? project : p
        ),
        isLoading: false,
      }));
      return project;
    } catch (error) {
      console.error("Error syncing project from Jira:", error);
      set({ isLoading: false });
      return null;
    }
  },
}));
