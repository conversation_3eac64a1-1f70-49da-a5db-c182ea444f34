import { create } from "zustand";
import type { SupabaseClient } from "@supabase/supabase-js";

export interface KanbanColumn {
  id: string;
  project_id: string;
  name: string;
  status: string;
  position: number;
  created_at: string;
}

interface KanbanColumnState {
  columns: KanbanColumn[];
  isLoading: boolean;
  error: string | null;
  creatingDefaultColumnsForProject: string | null;
  fetchColumns: (projectId: string, supabase: SupabaseClient) => Promise<void>;
  createColumn: (
    column: Omit<KanbanColumn, "id" | "created_at">,
    supabase: SupabaseClient
  ) => Promise<void>;
  updateColumn: (
    id: string,
    updates: Partial<Omit<KanbanColumn, "id" | "project_id" | "created_at">>,
    supabase: SupabaseClient
  ) => Promise<void>;
  deleteColumn: (id: string, supabase: SupabaseClient) => Promise<void>;
  reorderColumns: (
    columns: KanbanColumn[],
    supabase: SupabaseClient
  ) => Promise<void>;
}

const defaultKanbanColumnsData = [
  { name: "Backlog", status: "backlog", position: 0 },
  { name: "To Do", status: "todo", position: 1 },
  { name: "In Progress", status: "in-progress", position: 2 },
  { name: "Review", status: "review", position: 3 },
  { name: "Done", status: "done", position: 4 },
];

export const useKanbanColumnStore = create<KanbanColumnState>((set, get) => ({
  columns: [],
  isLoading: false,
  error: null,
  creatingDefaultColumnsForProject: null,

  fetchColumns: async (projectId, supabase) => {
    const { data, error } = await supabase
      .from("kanban_columns")
      .select("*")
      .eq("project_id", projectId)
      .order("position", { ascending: true });

    if (error) {
      console.error("Error fetching columns:", error);
      set({ isLoading: false, error: "Failed to fetch columns." });
      return;
    }

    if (data && data.length > 0) {
      set({ columns: data as KanbanColumn[], isLoading: false, error: null });
    } else if (projectId) {
      if (get().creatingDefaultColumnsForProject === projectId) {
        console.log(
          `Default column creation already in progress for project ${projectId}. Aborting this call.`
        );

        if (!get().isLoading) set({ isLoading: true });
        return;
      }

      set({ creatingDefaultColumnsForProject: projectId, isLoading: true });

      console.log(
        `No columns found for project ${projectId}. Creating default columns.`
      );
      const columnsToCreate = defaultKanbanColumnsData.map((col) => ({
        ...col,
        project_id: projectId,
      }));

      const { data: newDefaultColumns, error: insertError } = await supabase
        .from("kanban_columns")
        .insert(columnsToCreate)
        .select();

      if (insertError) {
        console.error("Error creating default columns:", insertError);
        set({
          isLoading: false,
          error: "Failed to create default columns.",
          creatingDefaultColumnsForProject: null,
        });
        return;
      }

      if (newDefaultColumns) {
        const sortedNewDefaults = newDefaultColumns.sort(
          (a, b) => a.position - b.position
        );
        set({
          columns: sortedNewDefaults as KanbanColumn[],
          isLoading: false,
          error: null,
          creatingDefaultColumnsForProject: null,
        });
      } else {
        set({
          columns: [],
          isLoading: false,
          error: "Default columns created but not returned.",
          creatingDefaultColumnsForProject: null,
        });
      }
    } else {
      set({ columns: [], isLoading: false, error: null });
    }
  },
  createColumn: async (column, supabase) => {
    set({ isLoading: true, error: null });
    const { data, error } = await supabase
      .from("kanban_columns")
      .insert([column])
      .select()
      .single();

    if (error || !data) {
      console.error("Error creating column:", error);
      set({ isLoading: false, error: "Failed to create column." });
      return;
    }

    set((state) => ({
      columns: [...state.columns, data as KanbanColumn].sort(
        (a, b) => a.position - b.position
      ),
      isLoading: false,
      error: null,
    }));
  },
  updateColumn: async (id, updates, supabase) => {
    set({ isLoading: true, error: null });
    const { data, error } = await supabase
      .from("kanban_columns")
      .update(updates)
      .eq("id", id)
      .select()
      .single();

    if (error || !data) {
      console.error("Error updating column:", error);
      set({ isLoading: false, error: "Failed to update column." });
      return;
    }

    set((state) => ({
      columns: state.columns.map((col) =>
        col.id === id ? (data as KanbanColumn) : col
      ),
      isLoading: false,
      error: null,
    }));
  },
  deleteColumn: async (id, supabase) => {
    const originalColumns = get().columns;
    set({ isLoading: true, error: null });

    set((state) => ({
      columns: state.columns.filter((col) => col.id !== id),
    }));

    const { error } = await supabase
      .from("kanban_columns")
      .delete()
      .eq("id", id);

    if (error) {
      console.error("Error deleting column:", error);
      set({
        columns: originalColumns,
        isLoading: false,
        error: "Failed to delete column.",
      });
      return;
    }

    set({ isLoading: false, error: null });
  },
  reorderColumns: async (orderedColumns, supabase) => {
    set({ isLoading: true, error: null });

    const updates = orderedColumns.map((col, index) => ({
      ...col,
      position: index,
    }));

    const { error } = await supabase
      .from("kanban_columns")
      .upsert(updates, { onConflict: "id" });

    if (error) {
      console.error("Error reordering columns:", error);

      let projectIdToRefetch: string | undefined;
      if (orderedColumns.length > 0) {
        projectIdToRefetch = orderedColumns[0].project_id;
      } else if (get().columns.length > 0) {
        projectIdToRefetch = get().columns[0].project_id;
      }

      if (projectIdToRefetch) {
        await get().fetchColumns(projectIdToRefetch, supabase);
        set({
          error: "Failed to save column order. Displaying last saved order.",
        });
      } else {
        set({
          isLoading: false,
          error: "Failed to save column order and could not refresh data.",
        });
      }
      return;
    }

    set({ columns: orderedColumns, isLoading: false, error: null });
  },
}));
