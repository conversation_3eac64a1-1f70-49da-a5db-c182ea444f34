import { create } from "zustand";
import { User, UserRole } from "../types";
import { getSupabaseClient } from "@/integrations/supabase/client";
import type { SupabaseClient } from "@supabase/supabase-js";

interface UserState {
  users: User[];
  isLoading: boolean;
  error: string | null;
  currentUser: User | null;
  fetchUsers: (supabaseClient?: SupabaseClient) => Promise<void>;
  createUser: (
    user: Omit<User, "id">,
    supabaseClient?: SupabaseClient
  ) => Promise<void>;
  setCurrentUser: (id: string) => void;
  getCurrentUserUuid: (clerkId: string, email: string) => string | null;
}

// Helper function to map database user to our User type
const mapDbUserToUser = (dbUser: any): User => ({
  id: dbUser.id,
  name: dbUser.name,
  email: dbUser.email,
  imageUrl: dbUser.image_url || "",
  role: dbUser.role as User<PERSON><PERSON>,
  clerkId: dbUser.clerk_id,
});

export const useUserStore = create<UserState>((set, get) => ({
  users: [],
  isLoading: false,
  error: null,
  currentUser: null,

  fetchUsers: async (supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase.from("users").select("*");

      if (error) {
        console.error("Error fetching users:", error);
        throw error;
      }

      if (!data) {
        console.warn("No data returned from users query");
        set({ users: [], isLoading: false });
        return;
      }

      const users = data.map((dbUser) => {
        return mapDbUserToUser(dbUser);
      });

      set({
        users,
        isLoading: false,
      });
    } catch (error: any) {
      console.error("Failed to fetch users:", error);
      set({
        isLoading: false,
        error: error?.message || "Failed to fetch users",
      });
    }
  },

  createUser: async (user, supabaseClient) => {
    const supabase = supabaseClient || getSupabaseClient(null);
    set({ isLoading: true, error: null });

    try {
      // Generate a UUID for the user
      const userId = crypto.randomUUID();

      const { error } = await supabase.from("users").insert({
        id: userId,
        name: user.name,
        email: user.email,
        image_url: user.imageUrl,
        role: user.role,
      });

      if (error) throw error;

      // Refetch to get the created user with id
      const { fetchUsers } = useUserStore.getState();
      await fetchUsers(supabase);

      set({ isLoading: false });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error?.message || "Failed to create user",
      });
    }
  },

  setCurrentUser: (id) => {
    const { users } = useUserStore.getState();
    const user = users.find((u) => u.id === id) || null;
    set({ currentUser: user });
  },

  getCurrentUserUuid: (clerkId: string, email?: string) => {
    const { users } = get();
    return (
      users.find((u) => u.clerkId === clerkId)?.id ||
      users.find((u) => u.email === email)?.id ||
      null
    );
  },
}));
