import { create } from "zustand";
import { SupabaseClient } from "@supabase/supabase-js";

export interface Column {
  id: string;
  projectId: string;
  name: string;
  order: number;
}

interface ColumnState {
  columns: Column[];
  fetchColumns: (projectId: string, supabase: SupabaseClient) => Promise<void>;
  createColumn: (
    name: string,
    projectId: string,
    supabase: SupabaseClient
  ) => Promise<void>;
  deleteColumn: (id: string, supabase: SupabaseClient) => Promise<void>;
  updateColumnOrder: (
    newOrder: Column[],
    supabase: SupabaseClient
  ) => Promise<void>;
}

export const useColumnStore = create<ColumnState>((set) => ({
  columns: [],

  fetchColumns: async (projectId, supabase) => {
    const { data, error } = await supabase
      .from("columns")
      .select("*")
      .eq("project_id", projectId)
      .order("order", { ascending: true });

    if (error) {
      console.error("Error fetching columns:", error);
      return;
    }

    set({ columns: data });
  },

  createColumn: async (name, projectId, supabase) => {
    const { data: currentCols, error: fetchError } = await supabase
      .from("columns")
      .select("order")
      .eq("project_id", projectId);

    const order = currentCols?.length ?? 0;

    const { data, error } = await supabase
      .from("columns")
      .insert([{ name, project_id: projectId, order }])
      .select();

    if (error) {
      console.error("Error creating column:", error);
      return;
    }

    set((state) => ({
      columns: [...state.columns, ...data],
    }));
  },

  deleteColumn: async (id, supabase) => {
    const { error } = await supabase.from("columns").delete().eq("id", id);

    if (error) {
      console.error("Error deleting column:", error);
      return;
    }

    set((state) => ({
      columns: state.columns.filter((col) => col.id !== id),
    }));
  },

  updateColumnOrder: async (newOrder, supabase) => {
    const updates = newOrder.map((col, index) => ({
      id: col.id,
      order: index,
    }));

    const { error } = await supabase.from("columns").upsert(updates);

    if (error) {
      console.error("Error updating column order:", error);
      return;
    }

    set({ columns: newOrder });
  },
}));
