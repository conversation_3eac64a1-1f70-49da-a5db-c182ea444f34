
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Square, CircleDot, CheckCircle2, Clock, Calendar } from "lucide-react";

const KanbanPreview = () => {
  // Define static data for the preview
  const columns = [
    { id: "backlog", title: "Backlog", icon: Square, color: "bg-gray-200" },
    { id: "todo", title: "To Do", icon: Square, color: "bg-blue-100" },
    { id: "in-progress", title: "In Progress", icon: CircleDot, color: "bg-yellow-100" },
    { id: "review", title: "In Review", icon: Clock, color: "bg-purple-100" },
    { id: "done", title: "Done", icon: CheckCircle2, color: "bg-green-100" },
  ];

  // Sample tickets for each column
  const tickets = {
    backlog: [
      { id: "b1", title: "Research competing products", priority: "low" },
      { id: "b2", title: "Define user personas", priority: "medium" },
    ],
    todo: [
      { id: "t1", title: "Design landing page", priority: "high" },
      { id: "t2", title: "Setup CI/CD pipeline", priority: "medium" },
    ],
    "in-progress": [
      { id: "ip1", title: "Implement authentication", priority: "high" },
      { id: "ip2", title: "Create dashboard layout", priority: "medium" },
    ],
    review: [
      { id: "r1", title: "Review API endpoints", priority: "medium" },
    ],
    done: [
      { id: "d1", title: "Project setup", priority: "high" },
      { id: "d2", title: "Define database schema", priority: "medium" },
    ],
  };

  const priorityColors = {
    low: "bg-gray-100 text-gray-800",
    medium: "bg-blue-100 text-blue-800",
    high: "bg-red-100 text-red-800",
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Kanban Board Preview</h3>
      
      <div className="flex space-x-4 overflow-x-auto pb-4">
        {columns.map((column) => (
          <div 
            key={column.id} 
            className="flex-shrink-0 w-72 bg-gray-50 rounded-md p-3"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <column.icon className="h-4 w-4 text-gray-500" />
                <h4 className="text-sm font-medium text-gray-700">{column.title}</h4>
              </div>
              <span className="flex h-5 w-5 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-700">
                {tickets[column.id as keyof typeof tickets].length}
              </span>
            </div>
            
            <div className="space-y-2">
              {tickets[column.id as keyof typeof tickets].map((ticket) => (
                <Card key={ticket.id} className="shadow-sm">
                  <CardContent className="p-3">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="text-xs text-gray-500">TASK-{ticket.id.toUpperCase()}</span>
                      <Badge 
                        variant="outline" 
                        className={priorityColors[ticket.priority as keyof typeof priorityColors]}
                      >
                        {ticket.priority}
                      </Badge>
                    </div>
                    <p className="text-sm font-medium">{ticket.title}</p>
                    <div className="mt-2 flex items-center text-xs text-gray-500">
                      <Calendar size={12} className="mr-1" />
                      <span>May 28</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default KanbanPreview;

