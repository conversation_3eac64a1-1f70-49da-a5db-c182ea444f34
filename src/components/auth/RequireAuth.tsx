import { Navigate, useLocation } from "react-router-dom";
import { useAuthContext } from "@/context/AuthContext";
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import { Loader2 } from "lucide-react";

interface RequireAuthProps {
  children: React.ReactNode;
  allowedRoles?: Array<'admin' | 'developer' | 'client'>;
}

const RequireAuth = ({ children, allowedRoles }: RequireAuthProps) => {
  const { userRole, isLoading } = useAuthContext();
  const location = useLocation();

  // Show loading indicator while checking authentication
  if (isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <>
      <SignedIn>
        {allowedRoles && !allowedRoles.includes(userRole as any) ? (
          // User is signed in but doesn't have the required role
          <Navigate to="/unauthorized" state={{ from: location }} replace />
        ) : (
          // User is signed in and has the required role (or no specific role is required)
          children
        )}
      </SignedIn>
      <SignedOut>
        <Navigate to="/sign-in" state={{ from: location }} replace />
      </SignedOut>
    </>
  );
};

export default RequireAuth;
