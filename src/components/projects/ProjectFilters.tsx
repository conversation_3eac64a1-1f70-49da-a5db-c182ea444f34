import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  X,
  Search,
  ListChecks,
  Flag,
  FilterX,
  ArrowDown,
  ArrowUp,
  AlertTriangle,
  Minus,
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface ProjectFiltersProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  filterStatus: string | null;
  onStatusChange: (status: string | null) => void;
  filterPriority: string | null;
  onPriorityChange: (priority: string | null) => void;
}

const ProjectFilters = ({
  searchQuery,
  onSearchChange,
  filterStatus,
  onStatusChange,
  filterPriority,
  onPriorityChange,
}: ProjectFiltersProps) => {
  const [searchInput, setSearchInput] = useState(searchQuery);

  useEffect(() => {
    setSearchInput(searchQuery);
  }, [searchQuery]);

  const handleSearch = () => {
    onSearchChange(searchInput);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const clearAllFilters = () => {
    setSearchInput("");
    onSearchChange("");
    onStatusChange(null);
    onPriorityChange(null);
  };

  const areFiltersActive = searchQuery || filterStatus || filterPriority;

  const statusOptions = [
    { value: "all", label: "All Statuses" },
    { value: "backlog", label: "Backlog", color: "bg-gray-400" },
    { value: "todo", label: "To Do", color: "bg-blue-500" },
    { value: "in-progress", label: "In Progress", color: "bg-purple-500" },
    { value: "review", label: "Review", color: "bg-yellow-500" },
    { value: "done", label: "Done", color: "bg-green-500" },
  ];

  const priorityOptions = [
    { value: "all", label: "All Priorities" },
    {
      value: "low",
      label: "Low",
      icon: ArrowDown,
      className: "text-green-600",
    },
    {
      value: "medium",
      label: "Medium",
      icon: Minus,
      className: "text-yellow-600",
    },
    {
      value: "high",
      label: "High",
      icon: ArrowUp,
      className: "text-orange-600",
    },
    {
      value: "urgent",
      label: "Urgent",
      icon: AlertTriangle,
      className: "text-red-600",
    },
  ];

  return (
    <div className="flex flex-col md:flex-row items-center gap-4 p-3 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="relative flex-grow w-full md:w-auto">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search tickets by title, description, or key..."
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          onKeyDown={handleKeyDown}
          className="pl-10 pr-10 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500/20 rounded-lg transition-colors duration-200"
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {searchInput ? (
            <X
              className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-600 transition-colors"
              onClick={() => {
                setSearchInput("");
                onSearchChange("");
              }}
            />
          ) : null}
        </div>
      </div>

      <div className="flex w-full md:w-auto items-center gap-2">
        <Select
          value={filterStatus || "all"}
          onValueChange={(value) =>
            onStatusChange(value === "all" ? null : value)
          }
        >
          <SelectTrigger className="w-full md:w-48 border-gray-300 hover:border-gray-400 data-[state=open]:border-blue-500 transition-colors">
            <div className="flex items-center gap-2">
              <ListChecks className="h-4 w-4 text-gray-500" />
              <SelectValue placeholder="Filter by status" />
            </div>
          </SelectTrigger>
          <SelectContent className="p-1 bg-white">
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                <div className="flex items-center gap-2.5">
                  {option.color ? (
                    <div className={`h-2 w-2 rounded-full ${option.color}`} />
                  ) : (
                    <div className="h-2 w-2" /> /* Placeholder for alignment */
                  )}
                  <span>{option.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={filterPriority || "all"}
          onValueChange={(value) =>
            onPriorityChange(value === "all" ? null : value)
          }
        >
          <SelectTrigger className="w-full md:w-48 border-gray-300 hover:border-gray-400 data-[state=open]:border-blue-500 transition-colors">
            <div className="flex items-center gap-2">
              <Flag className="h-4 w-4 text-gray-500" />
              <SelectValue placeholder="Filter by priority" />
            </div>
          </SelectTrigger>
          <SelectContent className="p-1 bg-white">
            {priorityOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                <div className="flex items-center gap-2.5">
                  {option.icon ? (
                    <option.icon className={`h-4 w-4 ${option.className}`} />
                  ) : (
                    <div className="w-4" /> /* Placeholder for alignment */
                  )}
                  <span>{option.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {areFiltersActive && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-gray-600 hover:bg-gray-100 hover:text-gray-800"
          >
            <FilterX className="h-4 w-4 mr-1.5" />
            Clear
          </Button>
        )}
      </div>
    </div>
  );
};

export default ProjectFilters;
