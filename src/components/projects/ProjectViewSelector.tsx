import { But<PERSON> } from "@/components/ui/button";
import { Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Grid3x3, List, Plus } from "lucide-react";

interface ProjectViewSelectorProps {
  activeTab: string;
  viewMode: "grid" | "list";
  onTabChange: (value: string) => void;
  onViewModeChange: (mode: "grid" | "list") => void;
  onAddColumnClick?: () => void;
}

const ProjectViewSelector = ({
  activeTab,
  viewMode,
  onTabChange,
  onViewModeChange,
  onAddColumnClick,
}: ProjectViewSelectorProps) => {
  return (
    <div className="flex justify-between items-center p-4 bg-white border-b border-gray-200 rounded-lg shadow-sm">
      <TabsList className="flex space-x-4">
        <TabsTrigger
          value="kanban"
          onClick={() => onTabChange("kanban")}
          className={`transition-colors duration-200 ${
            activeTab === "kanban"
              ? "text-blue-600 font-semibold"
              : "text-gray-600"
          } hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
        >
          Kanban
        </TabsTrigger>
        <TabsTrigger
          value="list"
          onClick={() => onTabChange("list")}
          className={`transition-colors duration-200 ${
            activeTab === "list"
              ? "text-blue-600 font-semibold"
              : "text-gray-600"
          } hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
        >
          List
        </TabsTrigger>
      </TabsList>

      <div className="flex items-center space-x-2">
        {activeTab === "list" && (
          <>
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => onViewModeChange("grid")}
              className="flex items-center transition-colors duration-200 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Grid3x3 className="h-4 w-4 mr-1" />
              <span className="text-sm">Grid</span>
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => onViewModeChange("list")}
              className="flex items-center transition-colors duration-200 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <List className="h-4 w-4 mr-1" />
              <span className="text-sm">List</span>
            </Button>
          </>
        )}
        <Button
          size="sm"
          variant="primary"
          onClick={onAddColumnClick}
          className="flex items-center"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add column
        </Button>
      </div>
    </div>
  );
};

export default ProjectViewSelector;
