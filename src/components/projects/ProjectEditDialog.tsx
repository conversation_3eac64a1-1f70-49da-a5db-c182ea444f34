import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useProjectStore } from "@/store/projectStore";
import { toast } from "sonner";
import { Trash2, Edit } from "lucide-react";
import { useAuth } from "@clerk/clerk-react";
import { getSupabaseClient } from "@/integrations/supabase/client";

type ProjectEditDialogProps = {
  project: {
    id: string;
    name: string;
    description: string;
    key: string;
  };
  onEdited?: () => void;
  onDeleted?: () => void;
};

const ProjectEditDialog: React.FC<ProjectEditDialogProps> = ({
  project,
  onEdited,
  onDeleted,
}) => {
  const [open, setOpen] = useState(false);
  const [editData, setEditData] = useState({
    name: project.name,
    description: project.description,
    key: project.key,
  });
  const [isLoading, setIsLoading] = useState(false);

  const { updateProject, deleteProject } = useProjectStore();
  const { getToken } = useAuth();

  const handleEdit = async () => {
    setIsLoading(true);
    try {
      const token = await getToken({
        template:
          "supabase",
      });
      if (token) {
        const supabase = getSupabaseClient(token);
        await updateProject(
          {
            id: project.id,
            name: editData.name,
            description: editData.description,
            key: editData.key,
          },
          supabase
        );
        toast("Project updated");
        setOpen(false);
        onEdited?.();
      } else {
        toast.error("Authentication error. Could not update project.");
      }
    } catch (error) {
      console.error("Failed to update project:", error);
      toast.error("An error occurred while updating the project.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      const token = await getToken({
        template:
          "supabase",
      });
      if (token) {
        const supabase = getSupabaseClient(token);
        await deleteProject(project.id, supabase);
        toast("Project deleted");
        setOpen(false);
        onDeleted?.();
      } else {
        toast.error("Authentication error. Could not delete project.");
      }
    } catch (error) {
      console.error("Failed to delete project:", error);
      toast.error("An error occurred while deleting the project.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="icon" variant="ghost" className="ml-2">
          <Edit className="w-4 h-4" />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Project</DialogTitle>
        </DialogHeader>
        <div className="space-y-3 py-2">
          <Input
            label="Project Name"
            value={editData.name}
            onChange={(e) =>
              setEditData((v) => ({ ...v, name: e.target.value }))
            }
            required
          />
          <Input
            label="Project Key"
            value={editData.key}
            onChange={(e) =>
              setEditData((v) => ({ ...v, key: e.target.value.toUpperCase() }))
            }
            required
            maxLength={5}
          />
          <Textarea
            label="Description"
            value={editData.description}
            onChange={(e) =>
              setEditData((v) => ({ ...v, description: e.target.value }))
            }
          />
        </div>
        <DialogFooter>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
          >
            <Trash2 className="w-4 h-4 mr-1" /> Delete
          </Button>
          <Button onClick={handleEdit} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectEditDialog;
