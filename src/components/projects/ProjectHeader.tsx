import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Plus, Settings, RefreshCw, Download } from "lucide-react";
import { Project } from "@/types";

interface ProjectHeaderProps {
  project: Project | null;
  onSyncProjectKey: () => Promise<void>;
  onCreateTicket: () => void;
  children?: React.ReactNode;
}

const ProjectHeader = ({
  project,
  onSyncProjectKey,
  onCreateTicket,
  children,
}: ProjectHeaderProps) => {
  const navigate = useNavigate();

  if (!project) return null;

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between p-5 bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
      {/* Left: Project info */}
      <div className="flex-1 min-w-0 pb-3 sm:pb-0 sm:pr-4 sm:border-r border-gray-200">
        <h1 className="text-xl sm:text-3xl font-semibold flex flex-wrap items-center gap-2 text-gray-800">
          <span className="truncate">{project.name}</span>
          {project.key ? (
            <span className="px-2 py-0.5 rounded-md bg-gray-100 text-xs font-mono text-gray-700 border border-gray-300">
              {project.key}
            </span>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="h-7 px-2 text-xs font-mono hover:bg-blue-50 hover:text-blue-600"
              onClick={onSyncProjectKey}
            >
              <RefreshCw className="h-3.5 w-3.5 mr-1" />
              Sync Key
            </Button>
          )}
        </h1>
        {project.description && (
          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
            {project.description}
          </p>
        )}
      </div>

      {/* Right: Actions */}
      <div className="flex flex-col gap-2 sm:flex-row sm:gap-3 shrink-0 w-full sm:w-auto">
        <Button
          size="sm"
          onClick={onCreateTicket}
          className="flex items-center justify-center gap-1 w-full sm:w-auto hover:bg-blue-50 hover:text-blue-600 focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span className="text-sm font-medium">New Ticket</span>
        </Button>

        {children && (
          <div className="w-full sm:w-auto">{children}</div>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="w-full sm:w-auto hover:bg-gray-50 hover:text-gray-700 focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              More
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-52 rounded-xl border border-gray-200 bg-white p-2 shadow-lg"
          >
            <DropdownMenuItem
              onClick={() => navigate(`/settings/projects/${project.id}`)}
              className="group flex cursor-pointer items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900"
            >
              <Settings className="h-4 w-4 text-gray-500 group-hover:text-gray-700" />
              Project Settings
            </DropdownMenuItem>
            <DropdownMenuItem
              className="group flex cursor-pointer items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900"
            >
              <Download className="h-4 w-4 text-gray-500 group-hover:text-gray-700" />
              Export Data
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default ProjectHeader;