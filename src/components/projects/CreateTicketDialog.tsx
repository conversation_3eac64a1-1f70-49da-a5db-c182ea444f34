import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface UserOption {
  id: string;
  name?: string | null;
  email?: string | null;
}

interface KanbanColumnOption {
  name: string;
  status: string;
}

interface CreateTicketDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ticket: {
    title: string;
    description: string;
    priority: string;
    type: string;
    status: string;
    assigneeIds: string[];
  };
  onTicketChange: (field: string, value: string | string[] | null) => void;
  users: UserOption[];
  availableStatuses: KanbanColumnOption[];
  onCreateTicket: () => void;
}

const CreateTicketDialog = ({
  isOpen,
  onClose,
  ticket,
  onTicketChange,
  onCreateTicket,
  availableStatuses,
  users,
}: CreateTicketDialogProps) => {
  const [assigneePopoverOpen, setAssigneePopoverOpen] = useState(false);

  const getAssigneeDisplayName = (assigneeId: string) => {
    const user = users.find((u) => u.id === assigneeId);
    return user ? user.name || user.email || user.id : assigneeId;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[480px]">
        <DialogHeader>
          <DialogTitle>Create New Ticket</DialogTitle>
          <DialogDescription>
            Add a new ticket to this project.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={ticket.title}
              onChange={(e) => onTicketChange("title", e.target.value)}
              placeholder="Ticket title"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={ticket.description}
              onChange={(e) => onTicketChange("description", e.target.value)}
              placeholder="Ticket description"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={ticket.priority || "medium"}
                onValueChange={(value) => onTicketChange("priority", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent className="bg-gray-50 rounded-md">
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    value="low"
                  >
                    Low
                  </SelectItem>
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    value="medium"
                  >
                    Medium
                  </SelectItem>
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    value="high"
                  >
                    High
                  </SelectItem>
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    value="urgent"
                  >
                    Urgent
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="type">Type</Label>
              <Select
                value={ticket.type || "task"}
                onValueChange={(value) => onTicketChange("type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent className="bg-gray-50 rounded-md">
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    value="bug"
                  >
                    Bug
                  </SelectItem>
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    value="feature"
                  >
                    Feature
                  </SelectItem>
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    value="task"
                  >
                    Task
                  </SelectItem>
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    value="improvement"
                  >
                    Improvement
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={
                ticket.status ||
                (availableStatuses && availableStatuses.length > 0
                  ? availableStatuses[0].status
                  : "")
              }
              onValueChange={(value) => onTicketChange("status", value)}
              disabled={!availableStatuses || availableStatuses.length === 0}
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent className="bg-gray-50 rounded-md">
                {(availableStatuses || []).map((col) => (
                  <SelectItem
                    className="hover:bg-gray-100 cursor-pointer"
                    key={col.status}
                    value={col.status}
                  >
                    {col.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {(!availableStatuses || availableStatuses.length === 0) && (
              <p className="text-xs text-muted-foreground pt-1">
                No statuses (columns) available for this project. Consider
                adding columns in the project settings.
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="assignees-popover-trigger">Assignees</Label>
            <Popover
              open={assigneePopoverOpen}
              onOpenChange={setAssigneePopoverOpen}
            >
              <PopoverTrigger asChild>
                <Button
                  id="assignees-popover-trigger"
                  variant="outline"
                  role="combobox"
                  aria-expanded={assigneePopoverOpen}
                  className="w-full justify-between"
                >
                  <span className="truncate">
                    {ticket.assigneeIds && ticket.assigneeIds.length > 0
                      ? ticket.assigneeIds.length === 1
                        ? getAssigneeDisplayName(ticket.assigneeIds[0])
                        : `${ticket.assigneeIds.length} users selected`
                      : "Select assignees..."}
                  </span>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[--radix-popover-trigger-width] p-0 bg-gray-50 rounded-md">
                <Command>
                  <CommandInput placeholder="Search users..." />
                  <CommandList>
                    <CommandEmpty>No users found.</CommandEmpty>
                    <CommandGroup>
                      {(users || []).map((user) => {
                        const isSelected = ticket.assigneeIds.includes(user.id);
                        return (
                          <CommandItem
                            className="hover:bg-gray-100 cursor-pointer"
                            key={user.id}
                            value={user.name || user.email || user.id}
                            onSelect={() => {
                              const currentAssignees = ticket.assigneeIds || [];
                              const newAssigneeIds = isSelected
                                ? currentAssignees.filter(
                                    (id) => id !== user.id
                                  )
                                : [...currentAssignees, user.id];
                              onTicketChange("assigneeIds", newAssigneeIds);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                isSelected ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {user.name || user.email || user.id}
                          </CommandItem>
                        );
                      })}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            {ticket.assigneeIds && ticket.assigneeIds.length > 0 && (
              <div className="pt-1 text-xs text-muted-foreground">
                Selected:{" "}
                {ticket.assigneeIds.map(getAssigneeDisplayName).join(", ")}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={onCreateTicket}>Create Ticket</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTicketDialog;
