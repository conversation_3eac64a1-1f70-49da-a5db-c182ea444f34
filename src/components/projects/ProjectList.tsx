import { Link } from "react-router-dom";
import { Ticket } from "@/types";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { CircleDot, Flag, User } from "lucide-react";
import Avatar from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface ProjectListProps {
  tickets: Ticket[];
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export const ProjectList = ({
  tickets,
  currentPage,
  totalPages,
  onPageChange,
}: ProjectListProps) => {
  return (
    <>
      <div className="bg-white rounded-md rounded-b-sm shadow-md border-none border-gray-500">
        {/* Header */}
        <div className="grid grid-cols-12 gap-4 px-4 py-3 border-b text-xs font-semibold text-gray-500 bg-gray-50">
          <div className="col-span-6">Ticket</div>
          <div className="col-span-2">Status</div>
          <div className="col-span-2">Priority</div>
          <div className="col-span-2">Assignee</div>
        </div>

        {tickets.length > 0 ? (
          tickets.map((ticket) => (
            <div
              key={ticket.id}
              className="grid grid-cols-12 gap-4 px-4 py-3 border-b text-sm items-center hover:bg-gray-50 transition-colors group"
            >
              <div className="col-span-6">
                <Link
                  to={`/tickets/${ticket.id}`}
                  className="text-gray-900 font-medium group-hover:text-blue-600 hover:underline transition-colors"
                >
                  {ticket.title}
                </Link>
              </div>

              <div className="col-span-2 flex items-center gap-2 text-gray-700">
                <CircleDot
                  size={14}
                  className={cn("text-gray-400", {
                    "text-green-500": ticket.status === "done",
                    "text-yellow-500": ticket.status === "in-progress",
                    "text-blue-500": ticket.status === "todo",
                  })}
                />
                <span className="capitalize">{ticket.status}</span>
              </div>

              <div className="col-span-2 flex items-center gap-2 text-gray-700">
                <Flag
                  size={14}
                  className={cn("text-gray-400", {
                    "text-red-500": ticket.priority === "high",
                    "text-yellow-500": ticket.priority === "medium",
                    "text-green-500": ticket.priority === "low",
                  })}
                />
                <span className="capitalize">{ticket.priority}</span>
              </div>

              {/* Assignees */}
              <div className="col-span-2 flex items-center gap-1 flex-wrap">
                {ticket.assignees && ticket.assignees.length > 0 ? (
                  ticket.assignees
                    .slice(0, 3)
                    .map((a) => (
                      <Avatar
                        key={a.id}
                        src={a.image_url}
                        alt={a.name || a.email || "User"}
                        size="xs"
                        title={a.name || a.email || "User"}
                        className="ring-1 ring-gray-300 shadow-sm"
                      />
                    ))
                ) : (
                  <div className="flex items-center text-xs text-gray-400">
                    <User size={14} className="mr-1" />
                    Unassigned
                  </div>
                )}
                {ticket.assignees && ticket.assignees.length > 3 && (
                  <span
                    className="text-xs px-1.5 py-0.5 bg-gray-200 text-gray-600 rounded-full"
                    title={ticket.assignees
                      .slice(3)
                      .map((a) => a.name || a.email)
                      .join(", ")}
                  >
                    +{ticket.assignees.length - 3}
                  </span>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="p-6 text-center text-gray-500 text-sm">
            No tickets found matching your filters.
          </div>
        )}
      </div>

      {/* Pagination */}
      {tickets.length > 0 && (
        <Pagination className="mt-4 flex justify-end">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => onPageChange(currentPage - 1)}
                className={cn({
                  "pointer-events-none opacity-50": currentPage === 1,
                })}
              />
            </PaginationItem>
            {Array.from({ length: totalPages }).map((_, i) => (
              <PaginationItem key={i}>
                <PaginationLink
                  isActive={currentPage === i + 1}
                  onClick={() => onPageChange(i + 1)}
                  className="cursor-pointer"
                >
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            ))}
            <PaginationItem>
              <PaginationNext
                onClick={() => onPageChange(currentPage + 1)}
                className={cn({
                  "pointer-events-none opacity-50": currentPage === totalPages,
                })}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </>
  );
};
