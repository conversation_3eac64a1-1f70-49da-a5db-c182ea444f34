import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@radix-ui/react-label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { User } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { Trash2 } from "lucide-react";

interface AddProjectMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddMember: (
    userId: string,
    employeeId: string | null,
    role: string
  ) => Promise<void>;
  onRemoveMember: (userId: string) => Promise<void>;
  onUpdateRole: (userId: string, newRole: string) => Promise<void>;
  availableUsers: User[];
  currentProjectMembers: Array<User & { role: string }>;
}

const AddProjectMemberDialog = ({
  isOpen,
  onClose,
  onAddMember,
  onRemoveMember,
  onUpdateRole,
  availableUsers,
  currentProjectMembers,
}: AddProjectMemberDialogProps) => {
  const { toast } = useToast();
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(
    null
  );
  const [selectedRole, setSelectedRole] = useState<string>("member");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setSelectedUserId("");
      setSelectedEmployeeId(null);
      setSelectedRole("member");
      setIsLoading(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedUserId) {
      const user = availableUsers.find((u) => u.id === selectedUserId);
      if (user && user.employee_id) {
        setSelectedEmployeeId(user.employee_id);
      } else {
        setSelectedEmployeeId(null);
      }
    } else {
      setSelectedEmployeeId(null);
    }
  }, [selectedUserId, availableUsers]);

  const handleAdd = async () => {
    if (!selectedUserId) {
      toast({
        title: "Error",
        description: "Please select a user.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      await onAddMember(selectedUserId, selectedEmployeeId, selectedRole);
      toast({ title: "Success", description: "Member added.", variant: "success" });
      
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add user.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemove = async (userId: string) => {
    try {
      await onRemoveMember(userId);
      toast({ title: "Removed", description: "Member removed.", variant: "success" });
    } catch {
      toast({
        title: "Error",
        description: "Could not remove user.",
        variant: "destructive",
      });
    }
  };

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      await onUpdateRole(userId, newRole);
      toast({ title: "Updated", description: "Role updated.", variant: "success" });
    } catch {
      toast({
        title: "Error",
        description: "Could not update role.",
        variant: "destructive",
      });
    }
  };

  const usersToAdd = availableUsers.filter(
    (user) => !currentProjectMembers.some((member) => member.id === user.id)
  );

  const sortedUsersToAdd = [...usersToAdd].sort((a, b) =>
    (a.name || a.email || "").localeCompare(b.name || b.email || "")
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full max-w-3xl sm:max-w-2xl">
        
          <DialogHeader className="px-6 pt-6">
            <DialogTitle>Manage Project Members</DialogTitle>
            <DialogDescription className="mb-4">
              View, edit, or add members to this project.
            </DialogDescription>
          </DialogHeader>

          {/* Scrollable body */}
          <div className="px-6 pb-4 max-h-[56vh] overflow-y-auto space-y-6">
            {/* Existing Members Section */}
            <div className="space-y-4">
          <h4 className="text-sm font-semibold">Current Members</h4>
          {currentProjectMembers.length === 0 ? (
            <p className="text-muted-foreground text-sm">No members yet.</p>
          ) : (
            <div className="grid gap-2">
              {currentProjectMembers.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between gap-4 border p-3 rounded-md"
                >
                  <div className="flex-1">
                    <div className="font-medium">
                      {member.name && member.name !== member.id && member.name !== member.email ? member.name : member.email || member.id}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {member.email || member.id}
                    </div>
                  </div>
                  <Select
                    value={member.role}
                    onValueChange={(val) => handleRoleChange(member.id, val)}
                  >
                    <SelectTrigger className="w-[120px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-50 rounded-md">
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="member"
                      >
                        Member
                      </SelectItem>
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="admin"
                      >
                        Admin
                      </SelectItem>
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="developer"
                      >
                        Developer
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleRemove(member.id)}
                    className="text-muted-foreground cursor-pointer hover:bg-red-600"
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              ))}
            </div>
          )}
          </div>

          {/* Add New Member Section */}
          <div className="pt-6 space-y-4 border-t mt-6">
          <h4 className="text-sm font-semibold">Add New Member</h4>
          <div className="grid gap-2">
            <Label htmlFor="user-select">User</Label>
            <Select onValueChange={setSelectedUserId} value={selectedUserId}>
              <SelectTrigger id="user-select">
                <SelectValue placeholder="Select a user" />
              </SelectTrigger>
              <SelectContent className="bg-gray-50 rounded-md">
                {sortedUsersToAdd.length > 0 ? (
                  sortedUsersToAdd.map((user) => (
                    <SelectItem
                      className="hover:bg-gray-100 cursor-pointer"
                      key={user.id}
                      value={user.id}
                    >
                      {user.name && user.name !== user.id ? user.name : user.email || user.id}
                    </SelectItem>
                  ))
                ) : (
                  <div className="py-2 px-3 text-sm text-muted-foreground">
                    No more users to add
                  </div>
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="role-select">Role</Label>
            <Select onValueChange={setSelectedRole} value={selectedRole}>
              <SelectTrigger id="role-select">
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent className="bg-gray-50 rounded-md">
                <SelectItem
                  className="hover:bg-gray-100 cursor-pointer"
                  value="member"
                >
                  Member
                </SelectItem>
                <SelectItem
                  className="hover:bg-gray-100 cursor-pointer"
                  value="admin"
                >
                  Admin
                </SelectItem>
                <SelectItem
                  className="hover:bg-gray-100 cursor-pointer"
                  value="developer"
                >
                  Developer
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          </div>
        </div>

        <DialogFooter className="px-6 py-4">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="hover:bg-gray-100 disabled:cursor-not-allowed cursor-pointer"
          >
            Close
          </Button>
          <Button
            onClick={handleAdd}
            disabled={!selectedUserId || isLoading}
            className="disabled:cursor-not-allowed cursor-pointer bg-blue-50 hover:bg-blue-600 hover:text-white "
          >
            {isLoading ? "Adding..." : "Add Member"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddProjectMemberDialog;
