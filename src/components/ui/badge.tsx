
// moved and renamed from Badge.tsx for consistent import casing
import React from 'react';
import { cn } from '@/lib/utils';
import { TicketPriority, TicketStatus, TicketType } from '@/types';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  className?: string;
}

export const Badge = ({ children, variant = 'default', className }: BadgeProps) => {
  const baseClasses = 'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium';
  
  const variantClasses = {
    default: "bg-primary/10 text-primary",
    secondary: "bg-secondary/10 text-secondary",
    destructive: "bg-destructive/10 text-destructive",
    outline:
      "border border-input bg-[hsl(var(--background))] text-[hsl(var(--foreground))] ",
  };

  return (
    <span className={cn(baseClasses, variantClasses[variant], className)}>
      {children}
    </span>
  );
};

export const PriorityBadge = ({ priority }: { priority: TicketPriority }) => {
  const priorityStyles = {
    low: 'bg-green-100 text-green-700',
    medium: 'bg-yellow-100 text-yellow-700',
    high: 'bg-orange-100 text-orange-700',
    urgent: 'bg-red-100 text-red-700',
  };
  
  return (
    <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${priorityStyles[priority]}`}>
      {priority.charAt(0).toUpperCase() + priority.slice(1)}
    </span>
  );
};

export const StatusBadge = ({ status }: { status: TicketStatus }) => {
  const statusStyles = {
    backlog: 'bg-gray-100 text-gray-700',
    todo: 'bg-blue-100 text-blue-700',
    'in-progress': 'bg-purple-100 text-purple-700',
    review: 'bg-indigo-100 text-indigo-700',
    done: 'bg-green-100 text-green-700',
  };
  
  const statusLabels = {
    backlog: 'Backlog',
    todo: 'To Do',
    'in-progress': 'In Progress',
    review: 'Review',
    done: 'Done',
  };
  
  return (
    <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${statusStyles[status]}`}>
      {statusLabels[status]}
    </span>
  );
};

export const TypeBadge = ({ type }: { type: TicketType }) => {
  const typeStyles = {
    bug: 'bg-red-100 text-red-700',
    feature: 'bg-blue-100 text-blue-700',
    task: 'bg-gray-100 text-gray-700',
    improvement: 'bg-purple-100 text-purple-700',
  };
  
  return (
    <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${typeStyles[type]}`}>
      {type.charAt(0).toUpperCase() + type.slice(1)}
    </span>
  );
};
