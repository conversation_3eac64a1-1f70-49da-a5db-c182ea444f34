
// moved and renamed from Avatar.tsx for consistent import casing
import React from "react";
import { cn } from "@/lib/utils";

interface AvatarProps {
  src?: string;
  alt?: string;
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  className?: string;
  fallback?: string;
  title?: string;
}

const Avatar = ({
  src,
  alt = "Avatar",
  size = "md",
  className,
  fallback,
  title
}: AvatarProps) => {
  const [error, setError] = React.useState(false);

  const sizeClasses = {
    xs: "h-6 w-6 text-xs",
    sm: "h-8 w-8 text-sm",
    md: "h-10 w-10 text-base",
    lg: "h-12 w-12 text-lg",
    xl: "h-14 w-14 text-xl",
  };

  const baseClasses =
    "inline-flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-medium overflow-hidden flex-shrink-0";

  const handleError = () => {
    setError(true);
  };

  const getFallback = () => {
    if (fallback) return fallback;
    if (!alt) return "?";

    const initials = alt
      .split(" ")
      .map((word) => word[0])
      .slice(0, 2)
      .join("")
      .toUpperCase();

    return initials || "?";
  };

  return (
    <div 
      className={cn(baseClasses, sizeClasses[size], className)} 
      title={title || alt}
    >
      {!error && src ? (
        <img
          src={src}
          alt={alt}
          className="h-full w-full object-cover"
          onError={handleError}
        />
      ) : (
        <span>{getFallback()}</span>
      )}
    </div>
  );
};

export default Avatar;
