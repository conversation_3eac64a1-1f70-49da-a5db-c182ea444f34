
import { Menu, <PERSON>, <PERSON>, Plus } from 'lucide-react';
import Avatar from '../ui/avatar'; // fixed import for casing
import { useUserStore } from '../../store/userStore';

interface HeaderProps {
  onOpenSidebar: () => void;
}

const Header = ({ onOpenSidebar }: HeaderProps) => {
  const { currentUser } = useUserStore();

  return (
    <header className="border-b bg-white">
      <div className="flex h-16 items-center justify-between px-4">
        {/* Left section */}
        <div className="flex items-center">
          <button
            className="mr-4 rounded-full p-2 text-gray-500 hover:bg-gray-100 lg:hidden"
            onClick={onOpenSidebar}
          >
            <Menu size={20} />
          </button>
          {/* Search */}
          <div className="hidden md:block">
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <Search size={16} className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search tickets..."
                className="w-64 rounded-md border border-gray-300 py-1.5 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
        {/* Right section */}
        <div className="flex items-center space-x-4">
          <button className="hidden rounded-md bg-blue-50 p-2 text-blue-700 hover:bg-blue-100 sm:flex sm:items-center sm:space-x-1">
            <Plus size={16} />
            <span className="text-sm font-medium">Create</span>
          </button>
          <button className="relative rounded-full p-2 text-gray-500 hover:bg-gray-100">
            <Bell size={20} />
            <span className="absolute -right-0.5 -top-0.5 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white">
              3
            </span>
          </button>
          <div className="relative">
            <button className="flex items-center space-x-1">
              <Avatar 
                src={currentUser?.imageUrl} 
                alt={currentUser?.name || 'User'} 
                size="sm" 
              />
              <span className="hidden text-sm font-medium md:block">
                {currentUser?.name}
              </span>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
