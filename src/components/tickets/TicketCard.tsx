import React, { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Link } from "react-router-dom";
import { PriorityBadge, TypeBadge } from "../ui/badge";
import Avatar from "../ui/avatar";
import { Ticket, User, UserProfile, TicketStatus } from "../../types";
import {
  Calendar,
  GitPullRequest,
  Check,
  ChevronsUpDown,
  MoreVertical,
  UserPlus,
  Trash2,
  X,
  ArrowRightLeft,
} from "lucide-react";
import { format } from "date-fns";
import { useUserStore } from "../../store/userStore";
import { useProjectStore } from "@/store/projectStore";
import { useTicketStore } from "@/store/ticketStore";
import type { SupabaseClient } from "@supabase/supabase-js";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { cn, userToUserProfile, getProfileDisplayName } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { useSupabase } from "@/hooks/useSupabase";

interface TicketCardProps {
  ticket: Ticket;
  supabase?: SupabaseClient | null;
  isSelected?: boolean;
  onSelect?: (ticketId: string, shiftKey: boolean) => void;
  isShiftPressed?: boolean;
  onBulkAssign?: (assigneeIds: string[]) => Promise<void>;
  onBulkStatusChange?: (newStatus: TicketStatus) => Promise<void>;
  onOpenBulkDelete?: () => void;
  onClearSelection?: () => void;
  /**
   * Project members as UserProfile (preferred).
   * Parent should pass this prop to avoid fallback logic.
   */
  projectMembers?: UserProfile[];
  /**
   * Users for display purposes (fallback).
   * Only used when projectMembers is not available.
   */
  users?: User[];
  /**
   * Count of selected tickets for display in bulk assign dialog.
   */
  selectedTicketsCount?: number;
  /**
   * Available statuses for bulk status change.
   */
  availableStatuses?: Array<{ id: string; name: string; status: TicketStatus }>;
}

const TicketCard = ({
  ticket,
  supabase,
  isSelected = false,
  onSelect,
  isShiftPressed = false,
  onBulkAssign,
  onBulkStatusChange,
  onOpenBulkDelete,
  onClearSelection,
  projectMembers = [],
  users: _propsUsers = [],
  selectedTicketsCount = 0,
  availableStatuses = [],
}: TicketCardProps) => {
  const { users: storeUsers } = useUserStore();
  const { currentProject } = useProjectStore();
  const { updateTicket, deleteTicket, fetchTickets } = useTicketStore();
  const { getFreshSupabaseClient } = useSupabase();
  const { toast } = useToast();
  const [assigneePopoverOpen, setAssigneePopoverOpen] = useState(false);
  const [tempAssignees, setTempAssignees] = useState<string[]>(
    ticket.assignees.map((a) => a.id) || []
  );
  const [bulkAssignDialogOpen, setBulkAssignDialogOpen] = useState(false);
  const [bulkTempAssignees, setBulkTempAssignees] = useState<string[]>([]);
  const [bulkStatusChangeDialogOpen, setBulkStatusChangeDialogOpen] =
    useState(false);
  const [bulkNewStatus, setBulkNewStatus] = useState<TicketStatus | "">("");
  const [singleDeleteDialogOpen, setSingleDeleteDialogOpen] = useState(false);
  const kebabButtonRef = React.useRef<HTMLButtonElement>(null);

  const assignees = ticket.assignees || [];

  /**
   * Member resolution strategy with clear fallback chain:
   * 1. projectMembers prop (preferred - passed by parent, already UserProfile shape)
   * 2. currentProject.members (extracted from project context)
   * 3. storeUsers (converted to UserProfile - last resort)
   *
   * Why three sources?
   * - projectMembers: Optimally passed from KanbanBoard to ensure consistent data
   * - currentProject.members: Fallback when parent doesn't provide explicit members
   * - storeUsers: Ensures ticket assignment always works even without project context
   */
  const availableMembers: UserProfile[] = (() => {
    // First priority: Use prop if provided
    if (projectMembers.length > 0) {
      return projectMembers;
    }

    // Second priority: Extract from current project
    const projectUsers =
      currentProject?.members
        ?.map((m) => m.user)
        .filter((u): u is UserProfile => u !== undefined && u !== null) || [];

    if (projectUsers.length > 0) {
      return projectUsers;
    }

    // Last resort: Convert store users to UserProfile shape
    return storeUsers.map(userToUserProfile);
  })();

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect?.(ticket.id, e.shiftKey);
  };

  const handleAssigneesChange = async (selectedUserIds: string[]) => {
    try {
      const freshSupabase = await getFreshSupabaseClient();
      await updateTicket(
        ticket.id,
        { assigneeIds: selectedUserIds },
        freshSupabase
      );

      toast({
        title: "Success",
        description: "Assignees updated successfully",
        variant: "success",
      });
    } catch (error) {
      console.error("Failed to update assignees:", error);
      toast({
        title: "Error",
        description: "Failed to update assignees. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSingleDelete = async () => {
    try {
      const freshSupabase = await getFreshSupabaseClient();
      await deleteTicket(ticket.id, freshSupabase);

      // Refetch tickets to update the UI
      if (currentProject?.id) {
        await fetchTickets(currentProject.id, freshSupabase);
      }

      toast({
        title: "Success",
        description: "Ticket deleted successfully",
        variant: "success",
      });

      setSingleDeleteDialogOpen(false);
    } catch (error) {
      console.error("Failed to delete ticket:", error);
      toast({
        title: "Error",
        description: "Failed to delete ticket. Please try again.",
        variant: "destructive",
      });
    }
  };

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: ticket.id,
    data: {
      type: "TICKET",
      ticket: ticket,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: "grab",
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={cn(
        "group relative block rounded-lg bg-white p-4 shadow-md transition-all hover:shadow-lg",
        isSelected && "ring-2 ring-blue-500 bg-blue-50"
      )}
      onClick={(e) => isDragging && e.preventDefault()}
    >
      {/* Selection Checkbox */}
      {(isShiftPressed || isSelected) && (
        <div
          className="absolute top-2 left-2 z-10 flex h-5 w-5 items-center justify-center rounded border-2 border-gray-400 bg-white cursor-pointer hover:border-blue-500"
          onClick={handleCheckboxClick}
          role="checkbox"
          aria-checked={isSelected}
          aria-label={`Select ticket ${ticket.reference}`}
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === " " || e.key === "Enter") {
              e.preventDefault();
              handleCheckboxClick(e as unknown as React.MouseEvent);
            }
          }}
        >
          {isSelected && <Check className="h-4 w-4 text-blue-600" />}
        </div>
      )}

      {/* Kebab Menu (shows  when selected) */}
      {isSelected && (
        <div className="absolute top-2 right-2 z-10 ">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <Button
                ref={kebabButtonRef}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-gray-100"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-48 bg-white"
              onCloseAutoFocus={(e) => e.preventDefault()}
            >
              <DropdownMenuItem
                className="hover:bg-blue-100 focus:bg-blue-100"
                onSelect={(e) => {
                  e.preventDefault();
                  setBulkAssignDialogOpen(true);
                }}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Assign Users
              </DropdownMenuItem>
              <DropdownMenuItem
                className="hover:bg-blue-100 focus:bg-blue-100"
                onSelect={(e) => {
                  e.preventDefault();
                  setBulkStatusChangeDialogOpen(true);
                }}
              >
                <ArrowRightLeft className="h-4 w-4 mr-2" />
                Change Status
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600 hover:bg-blue-100 focus:bg-blue-100"
                onSelect={(e) => {
                  e.preventDefault();
                  onOpenBulkDelete?.();
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="hover:bg-blue-100 focus:bg-blue-100"
                onSelect={(e) => {
                  e.preventDefault();
                  onClearSelection?.();
                }}
              >
                <X className="h-4 w-4 mr-2" />
                Clear Selection
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}

      {/* Bulk Assign Dialog - Proper modal without brittle positioning */}
      <Dialog
        open={bulkAssignDialogOpen}
        onOpenChange={setBulkAssignDialogOpen}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Assign Users to Selected Tickets</DialogTitle>
            <DialogDescription>
              Select users to assign to {selectedTicketsCount} selected
              ticket(s).
            </DialogDescription>
          </DialogHeader>
          <Command className="border rounded-md">
            <CommandInput
              placeholder="Search users..."
              className="text-sm h-10"
            />
            <CommandList>
              <CommandEmpty>No users found.</CommandEmpty>
              <CommandGroup>
                {availableMembers.map((member) => {
                  const displayName = getProfileDisplayName(member);
                  const isUserSelected = bulkTempAssignees.includes(member.id);
                  return (
                    <CommandItem
                      key={member.id}
                      value={displayName}
                      onSelect={() => {
                        setBulkTempAssignees((prev) =>
                          prev.includes(member.id)
                            ? prev.filter((id) => id !== member.id)
                            : [...prev, member.id]
                        );
                      }}
                      className="text-sm flex items-center hover:bg-gray-100"
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          isUserSelected ? "opacity-100" : "opacity-0"
                        )}
                      />
                      {displayName}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setBulkTempAssignees([]);
                setBulkAssignDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                if (bulkTempAssignees.length === 0) {
                  return;
                }
                await onBulkAssign?.(bulkTempAssignees);
                setBulkTempAssignees([]);
                setBulkAssignDialogOpen(false);
              }}
              disabled={bulkTempAssignees.length === 0}
            >
              Assign Users
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Status Change Dialog */}
      <Dialog
        open={bulkStatusChangeDialogOpen}
        onOpenChange={setBulkStatusChangeDialogOpen}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Change Status of Selected Tickets</DialogTitle>
            <DialogDescription>
              Select a new status for {selectedTicketsCount} selected ticket(s).
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <label className="text-sm font-medium mb-2 block">New Status</label>
            <select
              className="w-full border rounded-md p-2 text-sm"
              value={bulkNewStatus}
              onChange={(e) => setBulkNewStatus(e.target.value as TicketStatus)}
            >
              <option value="">-- Select Status --</option>
              {availableStatuses.map((statusOption) => (
                <option key={statusOption.id} value={statusOption.status}>
                  {statusOption.name}
                </option>
              ))}
            </select>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setBulkNewStatus("");
                setBulkStatusChangeDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                if (bulkNewStatus === "") {
                  return;
                }
                await onBulkStatusChange?.(bulkNewStatus);
                setBulkNewStatus("");
                setBulkStatusChangeDialogOpen(false);
              }}
              disabled={bulkNewStatus === ""}
            >
              Change Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div
        className={cn(
          "mb-3 flex justify-between items-center",
          (isShiftPressed || isSelected) && "ml-7"
        )}
      >
        <div className="flex items-center gap-2">
          {!isSelected && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-colors"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setSingleDeleteDialogOpen(true);
              }}
              title="Delete ticket"
            >
              <Trash2 className="h-3.5 w-3.5 text-gray-500 hover:text-red-600" />
            </Button>
          )}
          <span className="text-sm font-semibold text-gray-700">
            {ticket.reference ?? ""}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <PriorityBadge priority={ticket.priority} />
        </div>
      </div>
      <Link
        to={
          ticket.reference && currentProject?.key
            ? `/tickets/${currentProject.key}-${ticket.reference}`
            : `/tickets/${ticket.id}`
        }
        className="mb-3 line-clamp-2 text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200"
      >
        {ticket.title}
      </Link>
      <div className="mb-3 flex items-center space-x-3">
        <TypeBadge type={ticket.type} />
        {ticket.dueDate && (
          <div className="flex items-center text-sm text-gray-600">
            <Calendar size={14} className="mr-1" />
            {format(new Date(ticket.dueDate), "MMM d")}
          </div>
        )}
        {ticket.githubPRs?.length > 0 && (
          <div className="flex items-center text-sm text-gray-600">
            <GitPullRequest size={14} className="mr-1" />
            {ticket.githubPRs.length}
          </div>
        )}
      </div>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {ticket.labels?.slice(0, 2).map((label) => (
            <span
              key={label}
              className="rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-600"
            >
              {label}
            </span>
          ))}
          {ticket.labels && ticket.labels.length > 2 && (
            <span className="text-xs text-gray-500">
              +{ticket.labels.length - 2}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-1 lg:space-x-1.5">
          {ticket.assignees.slice(0, 3).map((assignee) => (
            <div
              key={assignee.id}
              className="relative flex items-center justify-center"
            >
              <Avatar
                src={assignee.image_url}
                alt={assignee.name || assignee.email || assignee.id}
                size="xs"
                title={assignee.name || assignee.email || assignee.id}
                className="ring-1 ring-gray-300 shadow-sm transition-shadow duration-200 hover:shadow-md focus:shadow-md"
              />
            </div>
          ))}
          {assignees.length > 3 && (
            <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs text-gray-600 font-medium shadow-md transition-shadow duration-200 hover:shadow-lg focus:shadow-lg cursor-pointer">
              +{ticket.assignees.length - 3}
            </span>
          )}
          {assignees.length === 0 && !isDragging && (
            <span className="text-xs text-gray-400 pt-1">Unassigned</span>
          )}
        </div>
      </div>
      {!isDragging && (
        <div className="mt-2">
          <label
            htmlFor={`assignees-popover-trigger-${ticket.id}`}
            className="text-sm text-gray-600 mb-1 block"
          >
            Assignees:
          </label>

          <Popover
            open={assigneePopoverOpen}
            onOpenChange={setAssigneePopoverOpen}
          >
            <PopoverTrigger asChild>
              <Button
                id={`assignees-popover-trigger-${ticket.id}`}
                variant="outline"
                role="combobox"
                aria-expanded={assigneePopoverOpen}
                className="w-full justify-between text-sm h-10"
              >
                <span className="truncate">
                  {ticket.assignees.length > 0
                    ? ticket.assignees.map(getProfileDisplayName).join(", ")
                    : "Assign users..."}
                </span>
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 text-gray-600" />
              </Button>
            </PopoverTrigger>

            <PopoverContent className="w-[--radix-popover-trigger-width] p-0 bg-gray-50">
              <Command>
                <CommandInput
                  placeholder="Search users..."
                  className="text-sm h-10"
                />
                <CommandList>
                  <CommandEmpty>No users found.</CommandEmpty>
                  <CommandGroup>
                    {availableMembers.map((profile) => {
                      const isSelectedUser = tempAssignees.includes(profile.id);
                      return (
                        <CommandItem
                          key={profile.id}
                          value={getProfileDisplayName(profile)}
                          onSelect={() => {
                            setTempAssignees((prev) =>
                              prev.includes(profile.id)
                                ? prev.filter((id) => id !== profile.id)
                                : [...prev, profile.id]
                            );
                          }}
                          className="text-sm flex items-center hover:bg-gray-100 transition-colors duration-200"
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              isSelectedUser ? "opacity-100" : "opacity-0"
                            )}
                          />
                          {getProfileDisplayName(profile)}
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                </CommandList>
                <div className="flex justify-end border-t px-2 py-1.5">
                  <Button
                    size="sm"
                    className="text-sm"
                    onClick={() => {
                      handleAssigneesChange(tempAssignees);
                      setAssigneePopoverOpen(false);
                    }}
                  >
                    Done
                  </Button>
                </div>
              </Command>
            </PopoverContent>
          </Popover>

          {ticket.assignees.length === 0 && !isDragging && (
            <p className="text-sm text-gray-400 pt-1">No assignees.</p>
          )}
        </div>
      )}

      {/* Single Ticket Delete Confirmation Dialog */}
      <AlertDialog
        open={singleDeleteDialogOpen}
        onOpenChange={setSingleDeleteDialogOpen}
      >
        <AlertDialogContent onClick={(e) => e.stopPropagation()}>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Ticket</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete ticket {ticket.reference}? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={(e) => e.stopPropagation()}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.stopPropagation();
                handleSingleDelete();
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default TicketCard;
