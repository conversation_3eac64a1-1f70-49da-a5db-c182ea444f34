import { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Link } from "react-router-dom";
import { PriorityBadge, TypeBadge } from "../ui/badge";
import Avatar from "../ui/avatar";
import { Ticket, User } from "../../types";
import { Calendar, GitPullRequest, Check, ChevronsUpDown } from "lucide-react";
import { format } from "date-fns";
import { useUserStore } from "../../store/userStore";
import { useProjectStore } from "@/store/projectStore";
import { useTicketStore } from "@/store/ticketStore";
import { useAuth } from "@clerk/clerk-react";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface TicketCardProps {
  ticket: Ticket;
}

const TicketCard = ({ ticket }: TicketCardProps) => {
  const { users } = useUserStore();
  const { currentProject } = useProjectStore();
  const { updateTicket } = useTicketStore();
  const { getToken } = useAuth();
  const [assigneePopoverOpen, setAssigneePopoverOpen] = useState(false);
  const [tempAssignees, setTempAssignees] = useState<string[]>(
    ticket.assignees.map((a) => a.id) || []
  );

  const assignees = users.filter((u) => ticket?.assigneeIds?.includes(u.id));
  const assignableUsers = currentProject?.members || [];
  const getUserDisplayName = (user: User) => user.name || user.email || user.id;

  const handleAssigneesChange = async (selectedUserIds: string[]) => {
    try {
      const token = await getToken({
        template:
          "supabase",
      });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);
      await updateTicket(ticket.id, { assigneeIds: selectedUserIds }, supabase);
    } catch (error) {
      console.error("Failed to update assignees:", error);
    }
  };

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: ticket.id,
    data: {
      type: "TICKET",
      ticket: ticket,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: "grab",
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="group block rounded-lg bg-white p-4 shadow-md transition-all hover:shadow-lg"
      onClick={(e) => isDragging && e.preventDefault()}
    >
      <div className="mb-3 flex justify-between items-center">
        <span className="text-sm font-semibold text-gray-700">
          {ticket.referenceKey}
        </span>
        <PriorityBadge priority={ticket.priority} />
      </div>
      <Link
        to={
          ticket.referenceKey && currentProject?.key
            ? `/tickets/${currentProject.key}-${ticket.referenceKey}`
            : `/tickets/${ticket.id}`
        }
        className="mb-3 line-clamp-2 text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200"
      >
        {ticket.title}
      </Link>
      <div className="mb-3 flex items-center space-x-3">
        <TypeBadge type={ticket.type} />
        {ticket.dueDate && (
          <div className="flex items-center text-sm text-gray-600">
            <Calendar size={14} className="mr-1" />
            {format(new Date(ticket.dueDate), "MMM d")}
          </div>
        )}
        {ticket.githubPRs?.length > 0 && (
          <div className="flex items-center text-sm text-gray-600">
            <GitPullRequest size={14} className="mr-1" />
            {ticket.githubPRs.length}
          </div>
        )}
      </div>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {ticket.labels?.slice(0, 2).map((label) => (
            <span
              key={label}
              className="rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-600"
            >
              {label}
            </span>
          ))}
          {ticket.labels && ticket.labels.length > 2 && (
            <span className="text-xs text-gray-500">
              +{ticket.labels.length - 2}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-1 lg:space-x-1.5">
          {ticket.assignees.slice(0, 3).map((assignee) => (
            <div
              key={assignee.id}
              className="relative flex items-center justify-center"
            >
              <Avatar
                src={assignee.image_url}
                alt={assignee.name || assignee.email || assignee.id}
                size="xs"
                title={assignee.name || assignee.email || assignee.id}
                className="ring-1 ring-gray-300 shadow-sm transition-shadow duration-200 hover:shadow-md focus:shadow-md"
              />
            </div>
          ))}
          {assignees.length > 3 && (
            <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs text-gray-600 font-medium shadow-md transition-shadow duration-200 hover:shadow-lg focus:shadow-lg cursor-pointer">
              +{ticket.assignees.length - 3}
            </span>
          )}
          {assignees.length === 0 && !isDragging && (
            <span className="text-xs text-gray-400 pt-1">Unassigned</span>
          )}
        </div>
      </div>
      {!isDragging && (
        <div className="mt-2">
          <label
            htmlFor={`assignees-popover-trigger-${ticket.id}`}
            className="text-sm text-gray-600 mb-1 block"
          >
            Assignees:
          </label>

          <Popover
            open={assigneePopoverOpen}
            onOpenChange={setAssigneePopoverOpen}
          >
            <PopoverTrigger asChild>
              <Button
                id={`assignees-popover-trigger-${ticket.id}`}
                variant="outline"
                role="combobox"
                aria-expanded={assigneePopoverOpen}
                className="w-full justify-between text-sm h-10"
              >
                <span className="truncate">
                  {ticket.assignees.length > 0
                    ? ticket.assignees.map(getUserDisplayName).join(", ")
                    : "Assign users..."}
                </span>
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 text-gray-600" />
              </Button>
            </PopoverTrigger>

            <PopoverContent className="w-[--radix-popover-trigger-width] p-0 bg-gray-50">
              <Command>
                <CommandInput
                  placeholder="Search users..."
                  className="text-sm h-10"
                />
                <CommandList>
                  <CommandEmpty>No users found.</CommandEmpty>
                  <CommandGroup>
                    {assignableUsers.map((user) => {
                      const isSelected = tempAssignees.includes(user.id);
                      return (
                        <CommandItem
                          key={user.id}
                          value={getUserDisplayName(user)}
                          onSelect={() => {
                            setTempAssignees((prev) =>
                              prev.includes(user.id)
                                ? prev.filter((id) => id !== user.id)
                                : [...prev, user.id]
                            );
                          }}
                          className="text-sm flex items-center hover:bg-gray-100 transition-colors duration-200"
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              isSelected ? "opacity-100" : "opacity-0"
                            )}
                          />
                          {getUserDisplayName(user)}
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                </CommandList>
                <div className="flex justify-end border-t px-2 py-1.5">
                  <Button
                    size="sm"
                    className="text-sm"
                    onClick={() => {
                      handleAssigneesChange(tempAssignees);
                      setAssigneePopoverOpen(false);
                    }}
                  >
                    Done
                  </Button>
                </div>
              </Command>
            </PopoverContent>
          </Popover>

          {ticket.assignees.length === 0 && !isDragging && (
            <p className="text-sm text-gray-400 pt-1">No assignees.</p>
          )}
        </div>
      )}
    </div>
  );
};

export default TicketCard;
