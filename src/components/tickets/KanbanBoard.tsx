import { useState, useEffect } from "react";
import {
  DndContext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
  defaultDropAnimationSideEffects,
  DragStartEvent,
} from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { useTicketStore } from "../../store/ticketStore";
import { useKanbanColumnStore } from "../../store/useKanbanColumnStore";
import { useAuth } from "@clerk/clerk-react";
import { Ticket, TicketStatus } from "../../types";
import TicketColumn from "./TicketColumn";
import TicketCard from "./TicketCard";
import Button from "../ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { DialogFooter, DialogHeader } from "../ui/dialog";
import Input from "../ui/input";
import { useProjectStore } from "@/store/projectStore";
import type { SupabaseClient } from "@supabase/supabase-js";

interface KanbanBoardProps {
  tickets: Ticket[];
}

const dropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.5",
      },
    },
  }),
};

const KanbanBoard = ({ tickets }: KanbanBoardProps) => {
  const {
    updateTicketStatus,
    reorderTicketsInColumn,
    updateTicketsOrder,
  } = useTicketStore();
  const {
    columns,
    fetchColumns,
    createColumn: createKanbanColumn,
    reorderColumns,
  } = useKanbanColumnStore();
  const { getToken } = useAuth();
  const [columnsMap, setColumnsMap] = useState<Record<string, Ticket[]>>({});
  const [activeTicket, setActiveTicket] = useState<Ticket | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [newColumnName, setNewColumnName] = useState("");
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null);

  const currentProject = useProjectStore((s) => s.currentProject);

  useEffect(() => {
    const initializeSupabase = async () => {
      const token = await getToken();
      if (token) {
        const { getSupabaseClient } = await import(
          "@/integrations/supabase/client"
        );
        setSupabase(getSupabaseClient(token));
      }
    };
    initializeSupabase();
  }, [getToken]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );


  useEffect(() => {
    const groupedTickets: Record<string, Ticket[]> = {};

    columns.forEach((column) => {
      groupedTickets[column.status] = [];
    });

    tickets.forEach((ticket) => {
      if (groupedTickets[ticket.status]) {
        groupedTickets[ticket.status].push(ticket);
      }
    });

    setColumnsMap(groupedTickets);
  }, [tickets, columns]);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const ticket = tickets.find((t) => t.id === active.id);
    if (ticket) {
      setActiveTicket(ticket);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTicket(null);

    if (!over) return;
    const activeId = active.id as string;
    const overId = over.id as string;

    if (activeId === overId) return;

    const isActiveAColumn = active.data.current?.type === "COLUMN";

    if (isActiveAColumn) {
      const oldIndex = columns.findIndex((col) => col.id === activeId);
      const newIndex = columns.findIndex((col) => col.id === overId);

      if (oldIndex !== newIndex) {
        const newColumnsOrder = arrayMove(columns, oldIndex, newIndex);

        useKanbanColumnStore.setState({ columns: newColumnsOrder });

        if (supabase) {
          reorderColumns(newColumnsOrder, supabase);
        }
      }
      return;
    }

    const draggedTicket = tickets.find((t) => t.id === activeId);
    if (!draggedTicket) return;

    let targetStatus: string | undefined;
    const overDataType = over.data.current?.type as string | undefined;

    if (overDataType === "COLUMN") {
      const targetColumn = columns.find((col) => col.id === overId);
      targetStatus = targetColumn?.status;
    } else if (overDataType === "TICKET") {
      const ticketBeingHovered = tickets.find((t) => t.id === overId);
      targetStatus = ticketBeingHovered?.status;
    } else {
      const targetColumnDirectly = columns.find((col) => col.id === overId);
      if (targetColumnDirectly) {
        targetStatus = targetColumnDirectly.status;
      }
    }

    if (targetStatus && draggedTicket.status !== targetStatus) {
      useTicketStore.setState((state) => ({
        tickets: state.tickets.map((t) =>
          t.id === draggedTicket.id
            ? { ...t, status: targetStatus as TicketStatus }
            : t
        ),
      }));

      const updateTicketStatusInDb = async () => {
        try {
          if (!supabase) throw new Error("Supabase client not initialized.");
          await updateTicketStatus(
            draggedTicket.id,
            targetStatus as TicketStatus,
            supabase
          );
        } catch (e) {
          console.error("Failed to update ticket status, reverting UI.", e);
          useTicketStore.setState({ tickets: tickets });
        }
      };
      updateTicketStatusInDb();
    }

    if (
      overDataType === "TICKET" &&
      targetStatus &&
      draggedTicket.status === targetStatus
    ) {
      const columnTickets = tickets
        .filter((t) => t.status === targetStatus)
        .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));
      const oldIndex = columnTickets.findIndex((t) => t.id === activeId);
      const newIndex = columnTickets.findIndex((t) => t.id === overId);

      if (oldIndex === -1 || newIndex === -1) return;

      const newOrderedTickets = arrayMove(columnTickets, oldIndex, newIndex);
      const newOrderedIds = newOrderedTickets.map((t) => t.id);

      reorderTicketsInColumn(targetStatus, newOrderedIds);

      const persistReorderToDb = async () => {
        try {
          if (!supabase) throw new Error("Supabase client not initialized.");

          const updates = newOrderedTickets.map((ticket, index) => ({
            id: ticket.id,
            position: index,
          }));

          await updateTicketsOrder(updates, supabase);
        } catch (e) {
          console.error("Failed to reorder tickets in DB, reverting UI.", e);
          useTicketStore.setState({ tickets });
          const { toast } = await import("@/components/ui/use-toast");
          toast({
            title: "Error",
            description:
              "Failed to save ticket order. Reverted to previous order.",
            variant: "destructive",
          });
        }
      };
      persistReorderToDb();
    }
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogTrigger asChild>
          <Button
            onClick={() => {
              setNewColumnName("");
              setDialogOpen(true);
            }}
            className="cursor-pointer bg-blue-600 text-white font-semibold py-2 px-4 rounded shadow hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200 mb-4"
          >
            Add Column
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <h2 className="text-xl font-bold text-gray-800">Add New Column</h2>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={newColumnName}
              onChange={(e) => setNewColumnName(e.target.value)}
              placeholder="Enter column name"
              className="border border-gray-300 rounded-md p-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={async () => {
                if (!newColumnName.trim() || !currentProject?.id || !supabase)
                  return;
                const newStatus =
                  newColumnName.trim().toLowerCase() === "to do"
                    ? "todo"
                    : newColumnName.toLowerCase().replace(/\s+/g, "-");
                const newPosition = columns.length;

                await createKanbanColumn(
                  {
                    name: newColumnName.trim(),
                    project_id: currentProject.id,
                    status: newStatus,
                    position: newPosition,
                  },
                  supabase
                );
                setDialogOpen(false);
                setNewColumnName("");
              }}
            >
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="w-full min-h-screen md:max-w-3xl lg:max-w-4xl xl:max-w-[65rem] 2xl:max-w-6xl mx-auto">
        <div className="flex h-full space-x-1.5 lg:space-x-2.5 overflow-x-auto pt-4 mt-3">
          <SortableContext items={columns.map((col) => col.id)}>
            {columns.map((column) => (
              <TicketColumn
                key={column.id}
                columnId={column.id}
                title={column.name}
                status={column.status}
                tickets={columnsMap[column.status] || []}
                count={columnsMap[column.status]?.length || 0}
                supabase={supabase}
              />
            ))}
          </SortableContext>
        </div>
      </div>

      <DragOverlay dropAnimation={dropAnimation}>
        {activeTicket ? <TicketCard ticket={activeTicket} /> : null}
      </DragOverlay>
    </DndContext>
  );
};

export default KanbanBoard;
