import { useState, useEffect } from "react";
import {
  Dnd<PERSON>ontext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
  defaultDropAnimationSideEffects,
  DragStartEvent,
} from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { useTicketStore } from "../../store/ticketStore";
import { useKanbanColumnStore } from "../../store/useKanbanColumnStore";
import { useAuth } from "@clerk/clerk-react";
import { Ticket, TicketStatus, UserProfile } from "../../types";
import TicketColumn from "./TicketColumn";
import TicketCard from "./TicketCard";
import type { SupabaseClient } from "@supabase/supabase-js";
import { useUserStore } from "../../store/userStore";
import { useProjectStore } from "@/store/projectStore";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "@/hooks/use-toast";

// Valid TicketStatus values for runtime validation
const VALID_TICKET_STATUSES: readonly TicketStatus[] = [
  "backlog",
  "todo",
  "in-progress",
  "review",
  "done",
] as const;

/**
 * Type guard to validate if a string is a valid TicketStatus
 * @param status - The status string to validate
 * @returns true if the status is a valid TicketStatus, false otherwise
 */
function isValidTicketStatus(status: string): status is TicketStatus {
  return VALID_TICKET_STATUSES.includes(status as TicketStatus);
}

interface KanbanBoardProps {
  tickets: Ticket[];
}

const dropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.5",
      },
    },
  }),
};

const KanbanBoard = ({ tickets }: KanbanBoardProps) => {
  const {
    updateTicketStatus,
    reorderTicketsInColumn,
    updateTicketsOrder,
    deleteTicket,
    fetchTickets,
  } = useTicketStore();
  const { columns, reorderColumns } = useKanbanColumnStore();
  const { getToken } = useAuth();
  const { users } = useUserStore();
  const { currentProject } = useProjectStore();
  const [columnsMap, setColumnsMap] = useState<Record<string, Ticket[]>>({});
  const [activeTicket, setActiveTicket] = useState<Ticket | null>(null);
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null);
  const [selectedTickets, setSelectedTickets] = useState<Set<string>>(
    new Set()
  );
  const [isShiftPressed, setIsShiftPressed] = useState(false);
  const [lastSelectedTicketId, setLastSelectedTicketId] = useState<
    string | null
  >(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    const initializeSupabase = async () => {
      const token = await getToken();
      if (token) {
        const { getSupabaseClient } = await import(
          "@/integrations/supabase/client"
        );
        setSupabase(getSupabaseClient(token));
      }
    };
    initializeSupabase();
  }, [getToken]);

  // Shift key tracking for multi-select
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Shift") {
        setIsShiftPressed(true);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === "Shift") {
        setIsShiftPressed(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  useEffect(() => {
    const groupedTickets: Record<string, Ticket[]> = {};

    columns.forEach((column) => {
      groupedTickets[column.status] = [];
    });

    tickets.forEach((ticket) => {
      if (groupedTickets[ticket.status]) {
        groupedTickets[ticket.status].push(ticket);
      }
    });

    setColumnsMap(groupedTickets);
  }, [tickets, columns]);

  // Handle ticket selection with column-scoped range selection
  const handleTicketSelect = (ticketId: string, shiftKey: boolean) => {
    if (!shiftKey) {
      // Single select/deselect
      const newSelection = new Set(selectedTickets);
      if (newSelection.has(ticketId)) {
        newSelection.delete(ticketId);
      } else {
        newSelection.add(ticketId);
      }
      setSelectedTickets(newSelection);
      setLastSelectedTicketId(ticketId);
    } else if (lastSelectedTicketId) {
      // Range select within the same column
      const clickedTicket = tickets.find((t) => t.id === ticketId);
      const lastTicket = tickets.find((t) => t.id === lastSelectedTicketId);

      if (
        clickedTicket &&
        lastTicket &&
        clickedTicket.status === lastTicket.status
      ) {
        // Get tickets in the same column sorted by position
        const columnTickets = columnsMap[clickedTicket.status] || [];
        const sortedTickets = [...columnTickets].sort(
          (a, b) => a.position - b.position
        );

        const lastIndex = sortedTickets.findIndex(
          (t) => t.id === lastSelectedTicketId
        );
        const clickedIndex = sortedTickets.findIndex((t) => t.id === ticketId);

        if (lastIndex !== -1 && clickedIndex !== -1) {
          const startIndex = Math.min(lastIndex, clickedIndex);
          const endIndex = Math.max(lastIndex, clickedIndex);

          const newSelection = new Set(selectedTickets);
          for (let i = startIndex; i <= endIndex; i++) {
            newSelection.add(sortedTickets[i].id);
          }
          setSelectedTickets(newSelection);
        }
      } else {
        // If different columns, just select the clicked ticket
        const newSelection = new Set(selectedTickets);
        newSelection.add(ticketId);
        setSelectedTickets(newSelection);
        setLastSelectedTicketId(ticketId);
      }
    } else {
      // No last selection, just select this one
      const newSelection = new Set(selectedTickets);
      newSelection.add(ticketId);
      setSelectedTickets(newSelection);
      setLastSelectedTicketId(ticketId);
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const ticket = tickets.find((t) => t.id === active.id);
    if (ticket) {
      setActiveTicket(ticket);
      // If dragging a ticket that's not selected, clear selection and select only this one
      if (!selectedTickets.has(ticket.id)) {
        setSelectedTickets(new Set([ticket.id]));
        setLastSelectedTicketId(ticket.id);
      }
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTicket(null);

    if (!over) return;
    const activeId = active.id as string;
    const overId = over.id as string;

    if (activeId === overId) return;

    const isActiveAColumn = active.data.current?.type === "COLUMN";

    if (isActiveAColumn) {
      const oldIndex = columns.findIndex((col) => col.id === activeId);
      const newIndex = columns.findIndex((col) => col.id === overId);

      if (oldIndex !== newIndex) {
        const newColumnsOrder = arrayMove(columns, oldIndex, newIndex);

        useKanbanColumnStore.setState({ columns: newColumnsOrder });

        if (supabase) {
          reorderColumns(newColumnsOrder, supabase);
        }
      }
      return;
    }

    const draggedTicket = tickets.find((t) => t.id === activeId);
    if (!draggedTicket) return;

    let targetStatus: string | undefined;
    const overDataType = over.data.current?.type as string | undefined;

    if (overDataType === "COLUMN") {
      const targetColumn = columns.find((col) => col.id === overId);
      targetStatus = targetColumn?.status;
    } else if (overDataType === "TICKET") {
      const ticketBeingHovered = tickets.find((t) => t.id === overId);
      targetStatus = ticketBeingHovered?.status;
    } else {
      const targetColumnDirectly = columns.find((col) => col.id === overId);
      if (targetColumnDirectly) {
        targetStatus = targetColumnDirectly.status;
      }
    }

    if (!targetStatus) return;

    // Handle reordering within the same column
    if (
      overDataType === "TICKET" &&
      draggedTicket.status === targetStatus
    ) {
      const columnTickets = tickets
        .filter((t) => t.status === targetStatus)
        .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));
      const oldIndex = columnTickets.findIndex((t) => t.id === activeId);
      const newIndex = columnTickets.findIndex((t) => t.id === overId);

      if (oldIndex === -1 || newIndex === -1 || oldIndex === newIndex) return;

      const newOrderedTickets = arrayMove(columnTickets, oldIndex, newIndex);
      const newOrderedIds = newOrderedTickets.map((t) => t.id);

      reorderTicketsInColumn(targetStatus, newOrderedIds);

      const persistReorderToDb = async () => {
        try {
          if (!supabase) throw new Error("Supabase client not initialized.");

          const updates = newOrderedTickets.map((ticket, index) => ({
            id: ticket.id,
            position: index,
          }));

          await updateTicketsOrder(updates, supabase);
        } catch (e) {
          console.error("Failed to reorder tickets in DB, reverting UI.", e);
          useTicketStore.setState({ tickets });
          const { toast } = await import("@/hooks/use-toast");
          toast({
            title: "Error",
            description:
              "Failed to save ticket order. Reverted to previous order.",
            variant: "destructive",
          });
        }
      };
      persistReorderToDb();
      return; // Exit early - don't run the status change logic below
    }

    // Handle moving to a different column
    if (draggedTicket.status !== targetStatus) {
      // Check if we're dragging a selected ticket - if so, move all selected tickets
      const ticketsToMove = selectedTickets.has(activeId)
        ? Array.from(selectedTickets)
            .map((id) => tickets.find((t) => t.id === id))
            .filter((t): t is Ticket => t !== undefined)
        : [draggedTicket];

      // Update UI
      useTicketStore.setState((state) => ({
        tickets: state.tickets.map((t) =>
          ticketsToMove.find((tt) => tt.id === t.id)
            ? { ...t, status: targetStatus as TicketStatus }
            : t
        ),
      }));

      // Update database for all moved tickets
      const updateTicketStatusInDb = async () => {
        try {
          if (!supabase) throw new Error("Supabase client not initialized.");
          await Promise.all(
            ticketsToMove.map((ticket) =>
              updateTicketStatus(
                ticket.id,
                targetStatus as TicketStatus,
                supabase
              )
            )
          );

          // Clear selection after successful multi-drag
          if (ticketsToMove.length > 1) {
            setSelectedTickets(new Set());
            setLastSelectedTicketId(null);
          }
        } catch (e) {
          console.error("Failed to update ticket status, reverting UI.", e);
          useTicketStore.setState({ tickets: tickets });
        }
      };
      updateTicketStatusInDb();
    }
  };

  // Bulk assign users to selected tickets
  const handleBulkAssign = async (assigneeIds: string[]) => {
    if (!supabase || selectedTickets.size === 0) return;

    try {
      const ticketIds = Array.from(selectedTickets);

      // Prepare tasks with IDs attached (prevents index mismatch)
      const tasks = ticketIds.map((ticketId) => ({
        ticketId,
        task: (async () => {
          // Delete existing assignees
          const { error: deleteError } = await supabase
            .from("ticket_assignees")
            .delete()
            .eq("ticket_id", ticketId);

          if (deleteError) throw deleteError;

          // Insert new assignees
          if (assigneeIds.length > 0) {
            const newAssignments = assigneeIds.map((userId) => ({
              ticket_id: ticketId,
              user_id: userId,
            }));

            const { error: insertError } = await supabase
              .from("ticket_assignees")
              .insert(newAssignments);

            if (insertError) throw insertError;
          }

          return ticketId;
        })(),
      }));

      // Run all tasks
      const results = await Promise.allSettled(tasks.map((t) => t.task));

      let successful = 0;
      let failed = 0;

      // Log & count — single pass
      results.forEach((result, index) => {
        const ticketId = tasks[index].ticketId;

        if (result.status === "fulfilled") {
          successful++;
        } else {
          failed++;
          console.error(`Failed to update ticket ${ticketId}:`, result.reason);
        }
      });

      // Always refetch tickets
      if (currentProject?.id) {
        await fetchTickets(currentProject.id, supabase);
      }

      // User feedback
      if (failed === 0) {
        toast({
          title: "Success",
          description: `Assigned users to ${successful} ticket(s)`,
          variant: "success",
        });
        setSelectedTickets(new Set());
        setLastSelectedTicketId(null);
      } else if (successful === 0) {
        toast({
          title: "Error",
          description: `Failed to assign users to all ${failed} ticket(s)`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Partial Success",
          description: `Assigned users to ${successful} ticket(s), but ${failed} failed. Check console for details.`,
          variant: "default",
        });
        setSelectedTickets(new Set());
        setLastSelectedTicketId(null);
      }
    } catch (error) {
      console.error("Failed to bulk assign:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred during bulk assignment",
        variant: "destructive",
      });
    }
  };

  // Bulk change status of selected tickets
  const handleBulkStatusChange = async (newStatus: TicketStatus) => {
    if (!supabase || selectedTickets.size === 0) return;

    try {
      const ticketIds = Array.from(selectedTickets);

      // Prepare tasks with IDs attached (prevents index mismatch)
      const tasks = ticketIds.map((ticketId) => ({
        ticketId,
        task: updateTicketStatus(ticketId, newStatus, supabase),
      }));

      // Run all tasks
      const results = await Promise.allSettled(tasks.map((t) => t.task));

      let successful = 0;
      let failed = 0;

      // Log & count — single pass
      results.forEach((result, index) => {
        const ticketId = tasks[index].ticketId;

        if (result.status === "fulfilled") {
          successful++;
        } else {
          failed++;
          console.error(`Failed to update ticket ${ticketId}:`, result.reason);
        }
      });

      // Always refetch tickets
      if (currentProject?.id) {
        await fetchTickets(currentProject.id, supabase);
      }

      // User feedback
      if (failed === 0) {
        toast({
          title: "Success",
          description: `Updated status for ${successful} ticket(s)`,
          variant: "success",
        });
        setSelectedTickets(new Set());
        setLastSelectedTicketId(null);
      } else if (successful === 0) {
        toast({
          title: "Error",
          description: `Failed to update status for all ${failed} ticket(s)`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Partial Success",
          description: `Updated status for ${successful} ticket(s), but ${failed} failed. Check console for details.`,
          variant: "default",
        });
        setSelectedTickets(new Set());
        setLastSelectedTicketId(null);
      }
    } catch (error) {
      console.error("Failed to bulk update status:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred during bulk status update",
        variant: "destructive",
      });
    }
  };

  // Bulk delete selected tickets
  const handleBulkDelete = async () => {
    if (!supabase || selectedTickets.size === 0) return;

    try {
      const ticketIds = Array.from(selectedTickets);

      // Prepare tasks with IDs attached (prevents index mismatch)
      const tasks = ticketIds.map((ticketId) => ({
        ticketId,
        task: deleteTicket(ticketId, supabase),
      }));

      // Run all tasks
      const results = await Promise.allSettled(tasks.map((t) => t.task));

      let successful = 0;
      let failed = 0;

      // Log & count — single pass
      results.forEach((result, index) => {
        const ticketId = tasks[index].ticketId;

        if (result.status === "fulfilled") {
          successful++;
        } else {
          failed++;
          console.error(`Failed to delete ticket ${ticketId}:`, result.reason);
        }
      });

      // Always refetch tickets
      if (currentProject?.id) {
        await fetchTickets(currentProject.id, supabase);
      }

      // User feedback
      if (failed === 0) {
        toast({
          title: "Success",
          description: `Deleted ${successful} ticket(s)`,
          variant: "success",
        });
        setSelectedTickets(new Set());
        setLastSelectedTicketId(null);
        setDeleteDialogOpen(false);
      } else if (successful === 0) {
        toast({
          title: "Error",
          description: `Failed to delete all ${failed} ticket(s)`,
          variant: "destructive",
        });
        setDeleteDialogOpen(false);
      } else {
        toast({
          title: "Partial Success",
          description: `Deleted ${successful} ticket(s), but ${failed} failed. Check console for details.`,
          variant: "default",
        });
        setSelectedTickets(new Set());
        setLastSelectedTicketId(null);
        setDeleteDialogOpen(false);
      }
    } catch (error) {
      console.error("Failed to bulk delete:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred during bulk deletion",
        variant: "destructive",
      });
      setDeleteDialogOpen(false);
    }
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="w-full min-h-screen md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl mx-auto">
        <div className="flex h-full space-x-1.5 lg:space-x-2.5 overflow-x-auto pt-2">
          <SortableContext items={columns.map((col) => col.id)}>
            {columns.map((column) => (
              <TicketColumn
                key={column.id}
                columnId={column.id}
                title={column.name}
                status={column.status}
                tickets={columnsMap[column.status] || []}
                count={columnsMap[column.status]?.length || 0}
                supabase={supabase}
                selectedTickets={selectedTickets}
                onTicketSelect={handleTicketSelect}
                isShiftPressed={isShiftPressed}
                onBulkAssign={handleBulkAssign}
                onBulkStatusChange={handleBulkStatusChange}
                onOpenBulkDelete={() => setDeleteDialogOpen(true)}
                onClearSelection={() => {
                  setSelectedTickets(new Set());
                  setLastSelectedTicketId(null);
                }}
                projectMembers={
                  currentProject?.members
                    ?.map((m) => m.user)
                    .filter(
                      (u): u is UserProfile => u !== undefined && u !== null
                    ) || []
                }
                users={users}
                availableStatuses={columns
                  .filter((col) => {
                    const isValid = isValidTicketStatus(col.status);
                    if (!isValid) {
                      console.warn(
                        `Invalid ticket status found in column "${col.name}": "${col.status}". Skipping from available statuses.`
                      );
                    }
                    return isValid;
                  })
                  .map((col) => ({
                    id: col.id,
                    name: col.name,
                    status: col.status as TicketStatus,
                  }))}
              />
            ))}
          </SortableContext>
        </div>
      </div>

      <DragOverlay dropAnimation={dropAnimation}>
        {activeTicket ? <TicketCard ticket={activeTicket} /> : null}
      </DragOverlay>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Selected Tickets</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedTickets.size} selected
              ticket(s)? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DndContext>
  );
};

export default KanbanBoard;
