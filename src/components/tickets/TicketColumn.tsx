import { useState } from "react";
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { MoreHorizontal, Trash2, Check, ChevronsUpDown } from "lucide-react";
import {
  Ticket,
  TicketPriority,
  TicketType,
  TicketStatus,
  UserProfile,
  User,
} from "../../types";
import TicketCard from "./TicketCard";
import { useTicketStore } from "@/store/ticketStore";
import { useProjectStore } from "../../store/projectStore";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogDescription,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useKanbanColumnStore } from "@/store/useKanbanColumnStore";
import { useAuth, useUser } from "@clerk/clerk-react";
import { useUserStore } from "@/store/userStore";
import { useToast } from "@/components/ui/use-toast";

import { cn } from "@/lib/utils";
import type { SupabaseClient } from "@supabase/supabase-js";
import Avatar from "../ui/avatar";

const getCurrentUserUuid = async (
  clerkUserId: string,
  supabase: SupabaseClient
): Promise<string | null> => {
  if (!clerkUserId) return null;

  try {
    const { data, error } = await supabase
      .from("users")
      .select("id")
      .eq("clerk_id", clerkUserId)
      .single();

    if (error) throw error;
    return data?.id || null;
  } catch (error) {
    console.error("Error fetching Supabase user UUID:", error);
    return null;
  }
};

interface TicketColumnProps {
  columnId: string;
  title: string;
  status: string;
  tickets: Ticket[];
  count: number;
  supabase: SupabaseClient | null;
}

const TicketColumn = ({
  columnId,
  title,
  status,
  tickets,
  count,
  supabase,
}: TicketColumnProps) => {
  const { createTicket } = useTicketStore();
  const { currentProject } = useProjectStore();
  const { columns: kanbanColumns, deleteColumn } = useKanbanColumnStore();
  const { users } = useUserStore();
  const { user } = useUser();
  const { getToken } = useAuth();
  const { toast } = useToast();

  const [isCreateTicketDialogOpen, setIsCreateTicketDialogOpen] =
    useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [titleInput, setTitleInput] = useState("");
  const [descriptionInput, setDescriptionInput] = useState("");
  const [typeInput, setTypeInput] = useState<TicketType>("task");
  const [priorityInput, setPriorityInput] = useState<TicketPriority>("medium");
  const [assigneeIdsInput, setAssigneeIdsInput] = useState<string[]>([]);
  const [statusInput, setStatusInput] = useState<string>(status);
  const [assigneePopoverOpen, setAssigneePopoverOpen] = useState(false);
  const [tempAssigneeIdsInput, setTempAssigneeIdsInput] = useState<string[]>(
    []
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isRenaming, setIsRenaming] = useState(false);
  const [newTitle, setNewTitle] = useState(title);
  const { fetchTickets } = useTicketStore();
  const assignableUsers = currentProject?.members || [];
  const selectedProjectId = currentProject?.id;

  const handleCreateTicket = () => {
    if (!selectedProjectId) {
      toast({
        title: "Error",
        description: "No project selected.",
        variant: "destructive",
      });
      return;
    }

    if (!supabase) return;

    setIsLoading(true);

    getCurrentUserUuid(user?.id || "", supabase)
      .then((currentUserUuid) => {
        if (!currentUserUuid) {
          throw new Error("Failed to get current user information.");
        }

        const reporterFromStore = users.find((u) => u.id === currentUserUuid);
        if (!reporterFromStore) {
          throw new Error("Reporter user data not found.");
        }

        return createTicket(
          {
            projectId: selectedProjectId,
            title: titleInput,
            description: descriptionInput,
            status: statusInput as TicketStatus,
            type: typeInput,
            priority: priorityInput,
            assigneeIds: assigneeIdsInput,
            reporter_id: currentUserUuid,
            labels: [],
          },
          supabase
        );
      })
      .then(() => {
        if (selectedProjectId) {
          fetchTickets(selectedProjectId, supabase);
        }

        setTitleInput("");
        setDescriptionInput("");
        setTypeInput("task");
        setPriorityInput("medium");
        setAssigneeIdsInput([]);
        setStatusInput(status);
        setTempAssigneeIdsInput([]);
        setIsCreateTicketDialogOpen(false);

        toast({
          title: "Ticket Created",
          description: "Ticket has been created successfully.",
        });
      })
      .catch((e) => {
        console.error("Error creating ticket:", e);
        toast({
          title: "Error",
          description: "Failed to create ticket.",
          variant: "destructive",
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleRename = async () => {
    if (newTitle.trim() && newTitle !== title) {
      const token = await getToken({ template: "supabase" });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);
      await updateColumn(columnId, { name: newTitle }, supabase);
    }
    setIsRenaming(false);
  };

  const handleDeleteColumn = async () => {
    if (tickets.length > 0) {
      setDeleteError(
        "Cannot delete column with existing tickets. Please move or delete tickets first."
      );

      return;
    }
    setDeleteError(null);

    if (!supabase) return;

    try {
      await deleteColumn(columnId, supabase);
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Failed to delete column:", error);
      setDeleteError("Failed to delete column. Please try again.");
    }
  };

  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({
      id: columnId,
      data: {
        type: "COLUMN",
        column: { id: columnId, status: status, title: title },
      },
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const availableStatusesForDialog = kanbanColumns.map((col) => ({
    name: col.name,
    status: col.status,
  }));

  const getUserDisplayName = (user: User) => user.name || user.email || user.id;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="flex w-full md:w-[10rem] lg:w-[13rem] xl:w-[15rem] flex-col rounded-lg bg-gray-100 p-4 shadow-sm max-w-sm"
    >
      <div className="mb-4 flex items-center justify-between w-full px-1 lg:px-1.5">
        {isRenaming ? (
          <input
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            onBlur={handleRename}
            onKeyDown={(e) => e.key === "Enter" && handleRename()}
            autoFocus
            className="border rounded px-2 py-1"
          />
        ) : (
          <h2
            className="text-lg font-semibold"
            onDoubleClick={() => setIsRenaming(true)}
          >
            {title} ({count})
          </h2>
        )}
        <div className="flex items-center space-x-1 w-fit">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="md" className="h-8 w-8 p-2">
                <MoreHorizontal className="h-4 w-4 text-gray-600" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="bg-white rounded-lg shadow-md p-2"
            >
              <DropdownMenuItem
                onSelect={() => {
                  setStatusInput(status);
                  setTempAssigneeIdsInput([...assigneeIdsInput]);
                  setIsCreateTicketDialogOpen(true);
                }}
                className="flex items-center p-2 cursor-pointer hover:bg-gray-100 focus:bg-gray-100 transition-colors duration-200"
              >
                <span className="text-gray-800">Add Ticket</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => {
                  setDeleteError(null);
                  setIsDeleteDialogOpen(true);
                }}
                className="flex items-center text-red-600 p-2 cursor-pointer hover:bg-red-50 focus:bg-red-50 transition-colors duration-200"
              >
                <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                <span>Delete Column</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <Dialog
          open={isCreateTicketDialogOpen}
          onOpenChange={setIsCreateTicketDialogOpen}
        >
          <DialogTrigger asChild>
            {/* Trigger is now part of DropdownMenu, Dialog opens programmatically */}
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px] w-full">
            <DialogHeader>
              <DialogTitle>Create Ticket</DialogTitle>
              <DialogDescription>
                Add a new ticket to this column.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4 w-full">
              <div className="grid gap-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={titleInput}
                  onChange={(e) => setTitleInput(e.target.value)}
                  placeholder="Ticket title"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={descriptionInput}
                  onChange={(e) => setDescriptionInput(e.target.value)}
                  placeholder="Ticket description"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="status-dialog">Status</Label>
                <Select
                  value={statusInput}
                  onValueChange={(value) => setStatusInput(value)}
                  disabled={
                    !availableStatusesForDialog ||
                    availableStatusesForDialog.length === 0
                  }
                >
                  <SelectTrigger id="status-dialog">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-50 rounded-md">
                    {(availableStatusesForDialog || []).map((col) => (
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        key={col.status}
                        value={col.status}
                      >
                        {col.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4 w-full">
                <div className="grid gap-2">
                  <Label>Type</Label>
                  <Select
                    value={typeInput}
                    onValueChange={(val) => setTypeInput(val as TicketType)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-50 rounded-md">
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="task"
                      >
                        Task
                      </SelectItem>
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="bug"
                      >
                        Bug
                      </SelectItem>
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="feature"
                      >
                        Feature
                      </SelectItem>
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="improvement"
                      >
                        Improvement
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label>Priority</Label>
                  <Select
                    value={priorityInput}
                    onValueChange={(val) =>
                      setPriorityInput(val as TicketPriority)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-50 rounded-md">
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="low"
                      >
                        Low
                      </SelectItem>
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="medium"
                      >
                        Medium
                      </SelectItem>
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="high"
                      >
                        High
                      </SelectItem>
                      <SelectItem
                        className="hover:bg-gray-100 cursor-pointer"
                        value="urgent"
                      >
                        Urgent
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor={`assignees-popover-trigger-new-${columnId}`}>
                  Assignees
                </Label>
                <Popover
                  open={assigneePopoverOpen}
                  onOpenChange={(isOpen) => {
                    setAssigneePopoverOpen(isOpen);
                    if (isOpen) {
                      setTempAssigneeIdsInput([...assigneeIdsInput]);
                    }
                  }}
                >
                  <PopoverTrigger asChild>
                    <Button
                      id={`assignees-popover-trigger-new-${columnId}`}
                      variant="outline"
                      role="combobox"
                      aria-expanded={assigneePopoverOpen}
                      className="w-full justify-between text-sm h-10 rounded-md border border-gray-300 shadow-sm px-3 py-2 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <span className="truncate">
                        {assigneeIdsInput.length > 0
                          ? users
                              .filter((u) => assigneeIdsInput.includes(u.id))
                              .map(getUserDisplayName)
                              .join(", ")
                          : "Assign users..."}
                      </span>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-[--radix-popover-trigger-width] p-0 
             bg-gray-50 shadow-md rounded-md border border-gray-200 
             overflow-hidden"
                  >
                    <Command className="flex flex-col h-full">
                      <CommandInput
                        placeholder="Search users..."
                        className="text-sm h-9 pl-3 pr-3 border-b border-gray-200"
                      />
                      <CommandList className="flex-1 overflow-y-auto">
                        <CommandEmpty className="p-3 text-sm text-gray-500">
                          No users found.
                        </CommandEmpty>
                        <CommandGroup className="divide-y divide-gray-200 bg-gray-50 rounded-md">
                          {assignableUsers.map((user) => (
                            <CommandItem
                              key={user.id}
                              value={user.id}
                              onSelect={() => {
                                setTempAssigneeIdsInput((prev) =>
                                  prev.includes(user.id)
                                    ? prev.filter((id) => id !== user.id)
                                    : [...prev, user.id]
                                );
                              }}
                              className={cn(
                                "text-sm flex items-center p-3 hover:bg-gray-100 cursor-pointer ",
                                tempAssigneeIdsInput.includes(user.id)
                                  ? "bg-gray-100"
                                  : ""
                              )}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  tempAssigneeIdsInput.includes(user.id)
                                    ? "opacity-100 text-green-500"
                                    : "opacity-0"
                                )}
                              />

                              {getUserDisplayName(user)}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                      <div className="flex justify-end border-t px-3 py-2 bg-gray-100">
                        <Button
                          size="sm"
                          className="text-xs"
                          onClick={() => {
                            setAssigneeIdsInput([...tempAssigneeIdsInput]);
                            setAssigneePopoverOpen(false);
                          }}
                        >
                          Done
                        </Button>
                      </div>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setStatusInput(status);

                  setTempAssigneeIdsInput([]);
                  setIsCreateTicketDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateTicket} disabled={isLoading}>
                {isLoading ? "Creating..." : "Create"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to delete this column?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone.
              {deleteError && (
                <p className="mt-2 text-sm text-red-600">{deleteError}</p>
              )}
              {!deleteError && tickets.length > 0 && (
                <p className="mt-2 text-sm text-yellow-600">
                  This column contains {tickets.length} ticket(s). You must move
                  or delete them before deleting the column.
                </p>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteError(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteColumn}
              disabled={tickets.length > 0 && !deleteError}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <div className="flex flex-col gap-2">
        <SortableContext
          items={tickets.map((t) => t.id)}
          strategy={verticalListSortingStrategy}
        >
          {[...tickets]
            .sort((a, b) => a.position - b.position)
            .map((ticket) => (
              <TicketCard key={ticket.id} ticket={ticket} supabase={supabase} />
            ))}
        </SortableContext>
      </div>
      <div className="mt-auto pt-4 space-y-2">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => {
            setStatusInput(status);

            setTempAssigneeIdsInput([...assigneeIdsInput]);
            setIsCreateTicketDialogOpen(true);
          }}
        >
          + Add Ticket
        </Button>
        <Button
          variant="destructive"
          size="sm"
          className="w-full"
          onClick={() => {
            setDeleteError(null);
            setIsDeleteDialogOpen(true);
          }}
        >
          <Trash2 className="mr-2 h-4 w-4" /> Delete Column
        </Button>
      </div>
    </div>
  );
};

export default TicketColumn;
