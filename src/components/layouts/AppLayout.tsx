import { useUser, User<PERSON><PERSON><PERSON> } from "@clerk/clerk-react";
import { Link, useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  Kanban,
  Settings,
  Menu,
  Users2Icon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";
import { useAuthContext } from "@/context/AuthContext";

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout = ({ children }: AppLayoutProps) => {
  const { pathname } = useLocation();
  const { user } = useUser();
  const { userRole, isLoading } = useAuthContext();
  const [open, setOpen] = useState(false);
  
  // Initialize sidebar state from localStorage
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebarCollapsed');
    return saved === 'true';
  });

  // Persist sidebar state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', String(isSidebarCollapsed));
  }, [isSidebarCollapsed]);

  if (isLoading) {
    // Enhanced loading spinner matching Dashboard style
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <div className="w-12 h-12 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>
      </div>
    );
  }

  const navItems = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
      roles: ["admin", "developer", "client"],
    },
    {
      title: "Projects",
      href: "/projects",
      icon: Kanban,
      roles: ["admin", "developer", "client"],
    },
    {
      title: "Settings",
      href: "/settings",
      icon: Settings,
      roles: ["admin"], // Only admins can see settings
    },
    {
      title: "Members",
      href: "/members",
      icon: Users2Icon,
      roles: ["admin"], // Only admins can see members
    },
  ];

  const filteredNavItems = navItems.filter(
    (item) => !item.roles || item.roles.includes(userRole as any)
  );

  const getPageTitle = () => {
    const route = pathname.split("/")[1];
    const titleMap: { [key: string]: string } = {
      dashboard: "Dashboard",
      projects: "Projects",
      board: "Board",
      settings: "Settings",
      members: "Members",
    };
    return titleMap[route] || "ProjectFlow";
  };


  interface NavLinkProps {
 href: string;
 title: string;
  icon: React.ComponentType<{ className?: string }>;
  isMobile?: boolean;
}

  const NavLink = ({ href, title, icon: Icon, isMobile = false }: NavLinkProps) => {
    const isActive = pathname === href || pathname.startsWith(`${href}/`);
    return (
      <Link
        to={href}
        className={cn(
          "group relative flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ease-in-out",
          isActive
            ? "bg-blue-50 text-blue-700 border border-blue-200 shadow-sm hover:bg-blue-100 hover:shadow-md"
            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 border border-transparent hover:border-gray-200 hover:shadow-sm",
          isMobile && "text-base py-3"
        )}
      >
        <Icon
          className={cn(
            "h-4 w-4 transition-colors duration-200",
            isActive
              ? "text-blue-600"
              : "text-gray-500 group-hover:text-gray-700"
          )}
        />
        <span className="font-medium">{title}</span>
        {isActive && (
          <div className="absolute right-2 w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
        )}
      </Link>
    );
  };

  return (
    <div className="flex min-h-screen flex-col bg-gray-50 overflow-x-hidden w-full">
      {/* Mobile Nav */}
      <header className={cn("sticky top-0 z-30 flex h-16 items-center gap-4 border-b border-gray-200 bg-white shadow-sm px-4 sm:px-6")}>
        {!isSidebarCollapsed && (
          <h1 className="hidden lg:block text-lg font-semibold text-gray-900">
            {getPageTitle()}
          </h1>
        )}

        {isSidebarCollapsed && (
          <Button
            variant="outline"
            size="icon"
            className="hidden lg:flex border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
            onClick={() => setIsSidebarCollapsed(false)}
          >
            <Menu className="h-5 w-5 text-gray-600" />
            <span className="sr-only">Expand sidebar</span>
          </Button>
        )}

        <Sheet open={open} onOpenChange={setOpen}>
          <SheetTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="shrink-0 lg:hidden border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
            >
              <Menu className="h-5 w-5 text-gray-600" />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent
            side="left"
            className="w-72 sm:max-w-xs bg-white border-gray-200"
          >
            <div className="flex flex-col h-full">
              {/* Mobile menu header */}
              <div className="flex items-center gap-3 px-2 py-4 border-b border-gray-100">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm font-bold shadow-sm">
                  P
                </div>
                <span className="text-lg font-bold text-gray-900">
                  ProjectFlow
                </span>
              </div>

              {/* Mobile navigation */}
              <nav className="flex-1 px-2 py-4 space-y-1">
                {filteredNavItems.map((item) => (
                  <NavLink
                    key={item.href}
                    href={item.href}
                    title={item.title}
                    icon={item.icon}
                    isMobile
                  />
                ))}
              </nav>

              {/* Mobile user info */}
              <div className="border-t border-gray-100 p-4">
                <div className="flex items-center gap-3">
                  <UserButton afterSignOutUrl="/sign-in" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900 truncate">
                      {user?.fullName || ""}
                    </p>
                    <p className="text-xs text-gray-500 capitalize">
                      {userRole || "User"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </SheetContent>
        </Sheet>

        {/* Header brand - show only when sidebar is collapsed (hidden while sidebar is open) */}
        {isSidebarCollapsed && (
          <Link
            to="/"
            className="flex items-center gap-2 font-bold text-gray-900 hover:text-blue-600 transition-colors duration-200"
          >
            <div className="w-7 h-7 bg-gradient-to-br from-blue-500 to-purple-600 rounded-md flex items-center justify-center text-white text-xs font-bold shadow-sm">
              P
            </div>
            ProjectFlow
          </Link>
        )}

        <div className="ml-auto flex items-center gap-2">
          <UserButton afterSignOutUrl="/sign-in" />
        </div>
      </header>

      <div className="flex flex-1">
        {/* Desktop Sidebar */}
        <aside className={cn(
          "hidden border-r border-gray-200 bg-white shadow-sm lg:flex lg:flex-col transition-all duration-300",
          isSidebarCollapsed ? "lg:w-0 lg:overflow-hidden" : "lg:w-64"
        )}>
          <div className="sticky top-0 flex h-full flex-col">
            <div className="flex h-16 items-center gap-3 border-b border-gray-100 px-6">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm font-bold shadow-sm">
                P
              </div>
              {!isSidebarCollapsed && (
                <span className="text-xl font-bold text-gray-900">
                  ProjectFlow
                </span>
              )}
              {!isSidebarCollapsed && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="ml-auto"
                  onClick={() => setIsSidebarCollapsed(true)}
                >
                  <Menu className="h-4 w-4" />
                </Button>
              )}
            </div>

            <nav className="flex-1 px-3 py-4 space-y-1">
              {filteredNavItems.map((item) => (
                <NavLink
                  key={item.href}
                  href={item.href}
                  title={item.title}
                  icon={item.icon}
                />
              ))}
            </nav>

            <div className="border-t border-gray-100 p-4">
              <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                <UserButton afterSignOutUrl="/sign-in" />
                {!isSidebarCollapsed && (
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900 truncate">
                      {user?.fullName || ""}
                    </p>
                    <p className="text-xs text-gray-500 capitalize flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      {userRole || "User"}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 bg-gray-50 overflow-x-auto">{children}</main>
      </div>
    </div>
  );
};

export default AppLayout;
