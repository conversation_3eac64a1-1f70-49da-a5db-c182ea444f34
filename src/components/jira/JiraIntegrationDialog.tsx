
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { useAuth } from "@clerk/clerk-react";
import { getSupabaseClient } from "@/integrations/supabase/client";
import { linkJiraProject, setupJiraWebhook } from '@/lib/jiraSync';
import { Loader2 } from "lucide-react";

type JiraIntegrationDialogProps = {
  projectId: string;
  onSuccess?: () => void;
};

const JiraIntegrationDialog: React.FC<JiraIntegrationDialogProps> = ({ projectId, onSuccess }) => {
  const [open, setOpen] = useState(false);
  const [jiraProjectKey, setJiraProjectKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const { getToken } = useAuth();

  const validateJiraKey = async () => {
    if (!jiraProjectKey) return false;
    
    setValidating(true);
    try {
      const response = await fetch(`/api/jira-projects?key=${jiraProjectKey}`);
      if (!response.ok) return false;
      const data = await response.json();
      return data.exists === true;
    } catch (e) {
      console.error("Error validating JIRA key:", e);
      return false;
    } finally {
      setValidating(false);
    }
  };

  const handleLink = async () => {
    setLoading(true);
    try {
      // First validate the JIRA key
      const isValid = await validateJiraKey();
      if (!isValid) {
        toast("JIRA project key not found. Please check and try again.");
        setLoading(false);
        return;
      }

      const token = await getToken({
        template:
          "supabase",
      });
      const supabase = getSupabaseClient(token);
      
      console.log("Linking project to JIRA with key:", jiraProjectKey);
      
      // Link the project to JIRA
      const { jiraProjectId } = await linkJiraProject(supabase, projectId, jiraProjectKey);
      
      // Set up the webhook
      await setupJiraWebhook(supabase, projectId, jiraProjectId);

      toast("Now syncing with JIRA Project: " + jiraProjectKey);
      setOpen(false);
      
      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (e) {
      toast("An error occurred while linking with JIRA.");
      console.error("Jira integration error:", e);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">Link JIRA Project</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Connect your JIRA project</DialogTitle>
          <DialogDescription>
            Enter your JIRA project key to link it and set up a webhook.
          </DialogDescription>
        </DialogHeader>
        <Input 
          value={jiraProjectKey} 
          onChange={(e) => setJiraProjectKey(e.target.value.toUpperCase())} 
          placeholder="e.g. PROJ"
          disabled={loading || validating}
          maxLength={10}
        />
        <DialogFooter>
          <Button 
            variant="outline"
            onClick={() => setOpen(false)} 
            disabled={loading || validating}
          >
            Cancel
          </Button>
          <Button onClick={handleLink} disabled={loading || validating || !jiraProjectKey}>
            {loading || validating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {validating ? "Validating..." : "Linking..."}
              </>
            ) : "Link & Setup Webhook"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default JiraIntegrationDialog;
