import { useClerk } from '@clerk/clerk-react';
import { useNavigate } from 'react-router-dom';

export const useSignOut = () => {
  const navigate = useNavigate();
  const { signOut } = useClerk();
  
  const handleSignOut = async () => {
    await signOut();
    navigate('/sign-in');
  };
  
  return handleSignOut;
};

export const getCurrentUser = async () => {
  // This is a placeholder for now and will be implemented later
  return null;
};

// Custom useAuth hook could be implemented here in the future
