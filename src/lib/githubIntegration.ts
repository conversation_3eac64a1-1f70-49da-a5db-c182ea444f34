
import { getSupabaseClient } from "@/integrations/supabase/client";

export async function connectGithubRepo(
  supabaseToken: string,
  projectId: string,
  repositoryOwner: string,
  repositoryName: string
) {
  try {
    const supabase = getSupabaseClient(supabaseToken);
    
    // Check if repo is already connected
    const { data: existingRepo, error: fetchError } = await supabase
      .from('github_repos')
      .select('*')
      .eq('project_id', projectId)
      .eq('repo_owner', repositoryOwner)
      .eq('repo_name', repositoryName)
      .maybeSingle();

    if (fetchError) throw new Error(`Error checking for existing repo: ${fetchError.message}`);

    if (existingRepo) {
      return {
        success: false,
        message: 'Repository is already connected to this project',
      };
    }

    // If not exists, add it
    const repoUrl = `https://github.com/${repositoryOwner}/${repositoryName}`;
    const { error: insertError } = await supabase.from('github_repos').insert({
      project_id: projectId,
      repo_owner: repositoryOwner,
      repo_name: repositoryName,
      repo_url: repoUrl,
    });

    if (insertError) throw new Error(`Failed to connect repo: ${insertError.message}`);

    return {
      success: true,
      message: `Successfully connected ${repositoryOwner}/${repositoryName}`,
    };
  } catch (error) {
    console.error('Error in connectGithubRepo:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function setupWebhook(
  repoOwner: string,
  repoName: string,
) {
  try {
    // This would typically call a serverless function to set up a GitHub webhook
    // Currently just returns a mock success
    return {
      success: true,
      webhookId: `mock-webhook-${Date.now()}`,
      message: `Webhook for ${repoOwner}/${repoName} set up successfully`,
    };
  } catch (error) {
    console.error('Error setting up webhook:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to set up webhook',
    };
  }
}
