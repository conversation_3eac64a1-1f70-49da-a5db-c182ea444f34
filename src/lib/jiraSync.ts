import { isUUID } from "../utils/validators.js";
import type { SupabaseClient } from "@supabase/supabase-js";

// Handle both Vite's import.meta.env and Node's process.env
const getEnv = (key: string, defaultValue: string = ''): string => {
  // For Node.js/CommonJS
  if (typeof process !== 'undefined' && process?.env?.[key]) {
    return process.env[key] as string;
  }
  
  // For Vite/ESM
  if (typeof import.meta !== 'undefined' && (import.meta as any)?.env?.[key]) {
    return ((import.meta as any).env[key] as string) || defaultValue;
  }
  
  return defaultValue;
};

const JIRA_API_BASE_URL = getEnv('VITE_JIRA_API_BASE_URL', '');

const JIRA_API_ROUTE = `${JIRA_API_BASE_URL}/api/jira-projects`;

export async function lookupJiraKeyByName(
  projectName: string
): Promise<string | null> {
  if (!projectName) return null;
  try {
    const fetchUrl = `${JIRA_API_ROUTE}?name=${encodeURIComponent(
      projectName
    )}`;
    console.log(
      `Looking up JIRA project with name: "${projectName}" using URL: ${fetchUrl}`
    );
    const resp = await fetch(fetchUrl);

    if (!resp.ok) {
      let errorDetails = resp.statusText;
      try {
        const errorBody = await resp.text();

        errorDetails += ` - Body: ${errorBody.substring(0, 500)}`;
      } catch (textError) {}
      console.error(`JIRA API request failed: ${resp.status} ${errorDetails}`);
      throw new Error(`JIRA fetch failed: ${resp.status} ${errorDetails}`);
    }

    const responseText = await resp.text();
    try {
      const result = JSON.parse(responseText);
      console.log("JIRA lookup result:", result);
      return result.key || null;
    } catch (parseError) {
      console.error(
        `[JIRA] Failed to parse JSON response for "${projectName}". Raw response: "${responseText.substring(
          0,
          200
        )}..."`,
        parseError
      );
      throw new Error(
        `Received non-JSON response from JIRA API proxy. Raw response snippet: ${responseText.substring(
          0,
          200
        )}`
      );
    }
  } catch (e) {
    if (e instanceof Error) {
      console.error(
        `[JIRA] Error fetching project key for "${projectName}": ${e.message}`,
        e
      );
    } else {
      console.error(
        `[JIRA] Error fetching project key for "${projectName}":`,
        e
      );
    }
    return null;
  }
}

export async function syncMissingProjectKeysFromJira(
  supabase: SupabaseClient,
  projects: any[]
) {
  console.log("Starting sync of missing project keys from JIRA");
  let syncedCount = 0;

  for (const p of projects) {
    if (!p.key) {
      console.log(`Project ${p.name} has no key, looking up in JIRA...`);
      const key = await lookupJiraKeyByName(p.name);
      if (key) {
        console.log(`Found key ${key} for project ${p.name}, updating...`);
        const { error } = await supabase
          .from("projects")
          .update({ key })
          .eq("id", p.id);

        if (error) {
          console.error("Failed to update project key:", error);
        } else {
          syncedCount++;
        }
      } else {
        console.log(`No JIRA key found for project ${p.name}`);
      }
    }
  }

  console.log(`Synced ${syncedCount} project keys from JIRA`);
  return syncedCount;
}

export const linkJiraProject = async (
  supabase: SupabaseClient,
  projectId: string,
  jiraKey: string
) => {
  if (!isUUID(projectId)) {
    throw new Error(
      `linkJiraProject: projectId is not a valid UUID: ${projectId}`
    );
  }
  console.log(
    `Linking project ${projectId} to JIRA project with key ${jiraKey}`
  );

  try {
    const { error: updateError } = await supabase
      .from("projects")
      .update({ key: jiraKey })
      .eq("id", projectId);

    if (updateError) {
      console.error("Failed to update project key:", updateError);
      throw updateError;
    }

    const jiraProjectId = `jira-${jiraKey}`;

    console.log(
      "linkJiraProject: projectId =",
      projectId,
      "jiraKey =",
      jiraKey
    );

    const { data: existingIntegration, error: checkError } = await supabase
      .from("project_jira_integrations")
      .select("*")
      .eq("project_id", projectId)
      .maybeSingle();

    if (checkError) {
      console.error("Failed to check existing integration:", checkError);
      throw checkError;
    }

    if (existingIntegration) {
      const { error } = await supabase
        .from("project_jira_integrations")
        .update({
          jira_project_id: jiraProjectId,
          jira_project_key: jiraKey,
          active: true,
        })
        .eq("project_id", projectId);

      if (error) throw error;
    } else {
      const { error } = await supabase
        .from("project_jira_integrations")
        .insert([
          {
            project_id: projectId,
            jira_project_id: jiraProjectId,
            jira_project_key: jiraKey,
            active: true,
          },
        ]);

      if (error) throw error;
    }

    console.log("Successfully linked project to JIRA");
    return {
      success: true,
      jiraProjectId,
    };
  } catch (error) {
    console.error("Failed to link project to JIRA:", error);
    throw error;
  }
};

export const setupJiraWebhook = async (
  supabase: SupabaseClient,
  localProjectId: string,
  jiraProjectId: string
) => {
  try {
    console.log(
      `Setting up webhook for project ${localProjectId} with JIRA project ${jiraProjectId}`
    );
    const webhookId = `webhook-${Math.floor(Math.random() * 1000000)}`;

    const { error } = await supabase
      .from("project_jira_integrations")
      .update({ webhook_id: webhookId })
      .eq("project_id", localProjectId)
      .eq("jira_project_id", jiraProjectId);

    if (error) {
      console.error("Failed to set up webhook:", error);
      throw error;
    }

    console.log("Successfully set up JIRA webhook");
    return {
      id: webhookId,
      active: true,
    };
  } catch (error) {
    console.error("Failed to set up JIRA webhook:", error);
    throw error;
  }
};

export const syncProjectKeyFromJira = async (
  supabase: SupabaseClient,
  projectId: string
): Promise<string | null> => {
  try {
    console.log(`Syncing JIRA key for project ${projectId}`);

    const { data: project, error: projectError } = await supabase
      .from("projects")
      .select("*")
      .eq("id", projectId)
      .single();

    if (projectError || !project) {
      console.error("Failed to get project:", projectError);
      return null;
    }

    const key = await lookupJiraKeyByName(project.name);

    if (key) {
      console.log(`Found JIRA key ${key} for project ${project.name}`);
      const { error: updateError } = await supabase
        .from("projects")
        .update({ key })
        .eq("id", projectId);

      if (updateError) {
        console.error("Failed to update project key:", updateError);
        return null;
      }

      await linkJiraProject(supabase, projectId, key);

      return key;
    } else {
      console.log(`No JIRA key found for project ${project.name}`);
      return null;
    }
  } catch (error) {
    console.error("Failed to sync project key from JIRA:", error);
    return null;
  }
};

export const syncJiraTickets = async (
  supabase: SupabaseClient,
  projectId: string
): Promise<{ success: boolean; count?: number; error?: string }> => {
  try {
    console.log(`Syncing tickets for project ${projectId} with JIRA`);

    const { data: integration, error: integrationError } = await supabase
      .from("project_jira_integrations")
      .select("*")
      .eq("project_id", projectId)
      .maybeSingle();

    if (integrationError || !integration) {
      console.error("No JIRA integration found:", integrationError);
      return { success: false, error: "No JIRA integration found" };
    }

    const jiraProjectKey = integration.jira_project_key;
    const jiraApiUrl = `https://your-domain.atlassian.net/rest/api/3/search?jql=project=${jiraProjectKey}`;
    const jiraAuth = Buffer.from(
      `${process.env.JIRA_EMAIL}:${process.env.JIRA_API_TOKEN}`
    ).toString("base64");

    const resp = await fetch(jiraApiUrl, {
      headers: {
        Authorization: `Basic ${jiraAuth}`,
        Accept: "application/json",
      },
    });

    if (!resp.ok) {
      const errorText = await resp.text();
      console.error("Failed to fetch from Jira:", errorText);
      return { success: false, error: errorText };
    }

    const jiraData = await resp.json();
    const issues = jiraData.issues || [];

    const tickets = issues.map((issue: any) => ({
      title: issue.fields.summary,
      description: issue.fields.description || "",
      status: issue.fields.status.name.toLowerCase(),
      priority: issue.fields.priority?.name?.toLowerCase() || "medium",
      jiraId: issue.id,
      jiraIssueKey: issue.key,
      projectId,
    }));

    let count = 0;
    for (const ticket of tickets) {
      const { error } = await supabase
        .from("tickets")
        .upsert(ticket, { onConflict: "jiraId" });

      if (!error) count++;
      else console.error("Failed to upsert ticket:", error);
    }

    return { success: true, count };
  } catch (error) {
    console.error("Failed to sync tickets with JIRA:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};
