import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { User, UserProfile } from "@/types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Transforms a User object to a UserProfile object.
 * This is needed because different parts of the application use different user shapes:
 * - User: from userStore (camelCase fields like imageUrl, clerkId)
 * - UserProfile: from database relations (snake_case fields like image_url, clerk_id)
 * 
 * @param user - User object from userStore
 * @returns UserProfile object compatible with database schema
 */
export function userToUserProfile(user: User): UserProfile {
  return {
    id: user.id,
    name: user.name,
    email: user.email,
    image_url: user.imageUrl || "",
    role: user.role,
    clerk_id: user.clerkId,
  };
}

/**
 * Gets a human-readable display name from a UserProfile.
 * Falls back to email if name is not available, then to ID as last resort.
 * 
 * @param profile - UserProfile object
 * @returns Display name string
 */
export function getProfileDisplayName(profile: UserProfile): string {
  return profile.name || profile.email || profile.id;
}

const logger = {
  info: (...args: any[]) => console.info(...args),
  warn: (...args: any[]) => console.warn(...args),
  error: (...args: any[]) => console.error(...args),
  debug: (...args: any[]) => console.debug(...args),
};

export default logger;
