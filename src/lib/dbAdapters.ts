import { <PERSON><PERSON> } from "@/integrations/supabase/types";
import {
  GitHubPR,
  Ticket,
  UserProfile,
  TicketStatus,
  TicketPriority,
  TicketType,
  Comment,
} from "../types";

export type DbTicket = {
  title?: string;
  description?: string;
  status?: TicketStatus;
  priority?: TicketPriority;
  type?: TicketType;
  ticket_assignees?: string | null;
  reporter_id?: string | null;
  project_id?: string;
  due_date?: string | null;
  estimated_hours?: number | null;
  labels?: string[];
  github_prs?: Json;
};

interface DbProfile {
  id: string;
  full_name: string;
  avatar_url?: string | null;
}

export interface DbTicketWithProfiles {
  id: string;
  title: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  type: TicketType;
  assignee_id: string | null;
  reporter_id: string | null;
  project_id: string;
  created_at: string;
  updated_at: string;
  due_date: string | null;
  estimated_hours: number | null;
  labels: string[];
  github_prs: Json | null;
  assignee?: DbProfile | null;
  reporter?: DbProfile | null;
}

export interface DbCommentWithProfile {
  id: string;
  ticket_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  user?: DbProfile | null;
}

export function mapDbTicketToTicket(dbTicket: DbTicketWithProfiles): Ticket {
  const mapProfileToUser = (
    profile: DbProfile | null | undefined
  ): UserProfile | null => {
    if (!profile) return null;
    return {
      id: profile.id,
      name: profile.full_name,
    };
  };

  return {
    id: dbTicket.id,
    title: dbTicket.title,
    description: dbTicket.description,
    status: dbTicket.status,
    priority: dbTicket.priority,
    type: dbTicket.type,
    assigneeId: dbTicket.assignee?.id || dbTicket.assignee_id || null,
    reporterId: dbTicket.reporter?.id || dbTicket.reporter_id || null,
    projectId: dbTicket.project_id,
    createdAt: dbTicket.created_at,
    updatedAt: dbTicket.updated_at,
    dueDate: dbTicket.due_date || undefined,
    estimatedHours: dbTicket.estimated_hours || undefined,
    labels: dbTicket.labels || [],
    githubPRs: mapDbGithubPRs(dbTicket.github_prs || null),
    assignee: mapProfileToUser(dbTicket.assignee),
    reporter: mapProfileToUser(dbTicket.reporter),
    referenceKey: dbTicket.reference,
  };
}

export function mapTicketToDbTicket(ticket: Partial<Ticket>): DbTicket {
  const dbTicket: DbTicket = {};

  if (ticket.title !== undefined) dbTicket.title = ticket.title;
  if (ticket.description !== undefined)
    dbTicket.description = ticket.description;
  if (ticket.status !== undefined) dbTicket.status = ticket.status;
  if (ticket.priority !== undefined) dbTicket.priority = ticket.priority;
  if (ticket.type !== undefined) dbTicket.type = ticket.type;
  if (ticket.assigneeId !== undefined) dbTicket.assignee_id = ticket.assigneeId;
  if (ticket.reporterId !== undefined) dbTicket.reporter_id = ticket.reporterId;
  if (ticket.projectId !== undefined) dbTicket.project_id = ticket.projectId;
  if (ticket.dueDate !== undefined) dbTicket.due_date = ticket.dueDate;
  if (ticket.estimatedHours !== undefined)
    dbTicket.estimated_hours = ticket.estimatedHours;
  if (ticket.labels !== undefined) dbTicket.labels = ticket.labels;

  return dbTicket;
}

function mapDbGithubPRs(githubPRsJson: Json | null): GitHubPR[] {
  if (!githubPRsJson) return [];
  try {
    if (Array.isArray(githubPRsJson)) {
      return githubPRsJson.map((pr: any) => ({
        id: pr.id,
        title: pr.title,
        url: pr.url,
        status: pr.status,
        createdAt: pr.createdAt || pr.created_at,
        updatedAt: pr.updatedAt || pr.updated_at,
      }));
    }
    return [];
  } catch (error) {
    console.error("Error parsing GitHub PRs:", error);
    return [];
  }
}

export function mapDbCommentToComment(
  dbComment: DbCommentWithProfile
): Comment {
  const mapProfileToUser = (
    profile: DbProfile | null | undefined
  ): UserProfile | null => {
    if (!profile) return null;
    return {
      id: profile.id,
      name: profile.full_name,
    };
  };

  return {
    id: dbComment.id,
    ticketId: dbComment.ticket_id,
    userId: dbComment.user_id,
    content: dbComment.content,
    createdAt: dbComment.created_at,
    updatedAt: dbComment.updated_at,
    user: mapProfileToUser(dbComment.user),
  };
}
