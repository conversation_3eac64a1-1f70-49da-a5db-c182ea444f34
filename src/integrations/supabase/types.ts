export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      daily_logs: {
        Row: {
          attachments: string[] | null;
          content: string;
          created_at: string | null;
          date: string;
          employee_id: string;
          hours_spent: number;
          id: string;
          project_id: string;
          updated_at: string | null;
        };
        Insert: {
          attachments?: string[] | null;
          content: string;
          created_at?: string | null;
          date: string;
          employee_id: string;
          hours_spent: number;
          id?: string;
          project_id: string;
          updated_at?: string | null;
        };
        Update: {
          attachments?: string[] | null;
          content?: string;
          created_at?: string | null;
          date?: string;
          employee_id?: string;
          hours_spent?: number;
          id?: string;
          project_id?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "daily_logs_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "daily_logs_project_id_fkey";
            columns: ["project_id"];
            isOneToOne: false;
            referencedRelation: "projects";
            referencedColumns: ["id"];
          }
        ];
      };
      documents: {
        Row: {
          created_at: string | null;
          employee_id: string;
          id: string;
          name: string;
          path: string;
          signed_url: string;
          type: string;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          employee_id: string;
          id?: string;
          name: string;
          path: string;
          signed_url: string;
          type: string;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          employee_id?: string;
          id?: string;
          name?: string;
          path?: string;
          signed_url?: string;
          type?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "documents_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          }
        ];
      };
      employee_bills: {
        Row: {
          amount: number;
          created_at: string | null;
          employee_id: string;
          id: string;
          name: string;
          updated_at: string | null;
        };
        Insert: {
          amount: number;
          created_at?: string | null;
          employee_id: string;
          id?: string;
          name: string;
          updated_at?: string | null;
        };
        Update: {
          amount?: number;
          created_at?: string | null;
          employee_id?: string;
          id?: string;
          name?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "employee_bills_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          }
        ];
      };
      employee_notes: {
        Row: {
          content: string;
          created_at: string;
          created_by: string;
          employee_id: string;
          id: string;
          type: Database["public"]["Enums"]["note_type"];
          updated_at: string;
        };
        Insert: {
          content: string;
          created_at?: string;
          created_by: string;
          employee_id: string;
          id?: string;
          type?: Database["public"]["Enums"]["note_type"];
          updated_at?: string;
        };
        Update: {
          content?: string;
          created_at?: string;
          created_by?: string;
          employee_id?: string;
          id?: string;
          type?: Database["public"]["Enums"]["note_type"];
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "employee_notes_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          }
        ];
      };
      employee_skills: {
        Row: {
          created_at: string;
          employee_id: string;
          proficiency: number;
          skill_id: string;
          updated_at: string;
          years_of_experience: number;
        };
        Insert: {
          created_at?: string;
          employee_id: string;
          proficiency: number;
          skill_id: string;
          updated_at?: string;
          years_of_experience: number;
        };
        Update: {
          created_at?: string;
          employee_id?: string;
          proficiency?: number;
          skill_id?: string;
          updated_at?: string;
          years_of_experience?: number;
        };
        Relationships: [
          {
            foreignKeyName: "employee_skills_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "employee_skills_skill_id_fkey";
            columns: ["skill_id"];
            isOneToOne: false;
            referencedRelation: "skills";
            referencedColumns: ["id"];
          }
        ];
      };
      employees: {
        Row: {
          bank_account: Json | null;
          business_email: string;
          created_at: string | null;
          currency: string;
          first_name: string;
          github_id: string | null;
          id: string;
          last_name: string;
          other_names: string | null;
          personal_email: string | null;
          role_title: string;
          role_type: string;
          salary: number;
          salary_currency: string;
          slack_id: string | null;
          start_date: string;
          updated_at: string | null;
        };
        Insert: {
          bank_account?: Json | null;
          business_email: string;
          created_at?: string | null;
          currency: string;
          first_name: string;
          github_id?: string | null;
          id?: string;
          last_name: string;
          other_names?: string | null;
          personal_email?: string | null;
          role_title: string;
          role_type: string;
          salary: number;
          salary_currency?: string;
          slack_id?: string | null;
          start_date: string;
          updated_at?: string | null;
        };
        Update: {
          bank_account?: Json | null;
          business_email?: string;
          created_at?: string | null;
          currency?: string;
          first_name?: string;
          github_id?: string | null;
          id?: string;
          last_name?: string;
          other_names?: string | null;
          personal_email?: string | null;
          role_title?: string;
          role_type?: string;
          salary?: number;
          salary_currency?: string;
          slack_id?: string | null;
          start_date?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      feedback: {
        Row: {
          content: string;
          created_at: string;
          created_by: string;
          employee_id: string;
          id: string;
          rating: number;
          reviewer_id: string | null;
          updated_at: string;
        };
        Insert: {
          content: string;
          created_at?: string;
          created_by: string;
          employee_id: string;
          id?: string;
          rating: number;
          reviewer_id?: string | null;
          updated_at?: string;
        };
        Update: {
          content?: string;
          created_at?: string;
          created_by?: string;
          employee_id?: string;
          id?: string;
          rating?: number;
          reviewer_id?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "feedback_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "feedback_reviewer_id_fkey";
            columns: ["reviewer_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          }
        ];
      };
      github_prs: {
        Row: {
          created_at: string;
          id: string;
          pr_number: number;
          pr_status: string;
          pr_title: string;
          pr_url: string;
          repo_full_name: string;
          ticket_id: string | null;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          pr_number: number;
          pr_status: string;
          pr_title: string;
          pr_url: string;
          repo_full_name: string;
          ticket_id?: string | null;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          pr_number?: number;
          pr_status?: string;
          pr_title?: string;
          pr_url?: string;
          repo_full_name?: string;
          ticket_id?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "github_prs_ticket_id_fkey";
            columns: ["ticket_id"];
            isOneToOne: false;
            referencedRelation: "tickets";
            referencedColumns: ["id"];
          }
        ];
      };
      ticket_dependencies: {
        Row: {
          id: string;
          ticket_id: string;
          related_ticket_id: string;
          relationship_type: string;
        };
        Insert: {
          id?: string;
          ticket_id: string;
          related_ticket_id: string;
          relationship_type: string;
        };
        Update: {
          id?: string;
          ticket_id?: string;
          related_ticket_id?: string;
          relationship_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: "ticket_dependencies_ticket_id_fkey";
            columns: ["ticket_id"];
            referencedRelation: "tickets";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "ticket_dependencies_related_ticket_id_fkey";
            columns: ["related_ticket_id"];
            referencedRelation: "tickets";
            referencedColumns: ["id"];
          }
        ];
      };
      comments: {
        Row: {
          id: string;
          ticket_id: string;
          content: string;
          author_name: string | null;
          user_id: string | null;
          created_at: string;
          updated_at: string | null;
          jira_comment_id: string | null;
        };
        Insert: {
          id?: string;
          ticket_id: string;
          content: string;
          author_name?: string | null;
          user_id?: string | null;
          created_at?: string;
          updated_at?: string | null;
          jira_comment_id?: string | null;
        };
        Update: {
          id?: string;
          ticket_id?: string;
          content?: string;
          author_name?: string | null;
          user_id?: string | null;
          created_at?: string;
          updated_at?: string | null;
          jira_comment_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "comments_ticket_id_fkey";
            columns: ["ticket_id"];
            isOneToOne: false;
            referencedRelation: "tickets";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "comments_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      github_repos: {
        Row: {
          created_at: string;
          id: string;
          project_id: string | null;
          repo_name: string;
          repo_owner: string;
          repo_url: string;
          updated_at: string;
          webhook_id: string | null;
        };
        Insert: {
          created_at?: string;
          id?: string;
          project_id?: string | null;
          repo_name: string;
          repo_owner: string;
          repo_url: string;
          updated_at?: string;
          webhook_id?: string | null;
        };
        Update: {
          created_at?: string;
          id?: string;
          project_id?: string | null;
          repo_name?: string;
          repo_owner?: string;
          repo_url?: string;
          updated_at?: string;
          webhook_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "github_repos_project_id_fkey";
            columns: ["project_id"];
            isOneToOne: false;
            referencedRelation: "projects";
            referencedColumns: ["id"];
          }
        ];
      };
      invoices: {
        Row: {
          amount: number;
          created_at: string | null;
          currency: string;
          description: string;
          due_date: string;
          employee_id: string;
          id: string;
          issue_date: string;
          paid_date: string | null;
          project_id: string;
          status: string;
          updated_at: string | null;
        };
        Insert: {
          amount: number;
          created_at?: string | null;
          currency: string;
          description: string;
          due_date: string;
          employee_id: string;
          id?: string;
          issue_date: string;
          paid_date?: string | null;
          project_id: string;
          status: string;
          updated_at?: string | null;
        };
        Update: {
          amount?: number;
          created_at?: string | null;
          currency?: string;
          description?: string;
          due_date?: string;
          employee_id?: string;
          id?: string;
          issue_date?: string;
          paid_date?: string | null;
          project_id?: string;
          status?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "invoices_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "invoices_project_id_fkey";
            columns: ["project_id"];
            isOneToOne: false;
            referencedRelation: "projects";
            referencedColumns: ["id"];
          }
        ];
      };
      jira_projects: {
        Row: {
          created_at: string;
          id: string;
          jira_id: string;
          jira_key: string;
          jira_url: string;
          project_id: string | null;
          updated_at: string;
          webhook_id: string | null;
        };
        Insert: {
          created_at?: string;
          id?: string;
          jira_id: string;
          jira_key: string;
          jira_url: string;
          project_id?: string | null;
          updated_at?: string;
          webhook_id?: string | null;
        };
        Update: {
          created_at?: string;
          id?: string;
          jira_id?: string;
          jira_key?: string;
          jira_url?: string;
          project_id?: string | null;
          updated_at?: string;
          webhook_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "jira_projects_project_id_fkey";
            columns: ["project_id"];
            isOneToOne: true;
            referencedRelation: "projects";
            referencedColumns: ["id"];
          }
        ];
      };
      leave_requests: {
        Row: {
          created_at: string | null;
          employee_id: string;
          end_date: string;
          id: string;
          reason: string;
          start_date: string;
          status: string;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          employee_id: string;
          end_date: string;
          id?: string;
          reason: string;
          start_date: string;
          status: string;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          employee_id?: string;
          end_date?: string;
          id?: string;
          reason?: string;
          start_date?: string;
          status?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "leave_requests_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          }
        ];
      };
      project_github_integrations: {
        Row: {
          active: boolean | null;
          created_at: string;
          id: string;
          project_id: string;
          repo_full_name: string;
          repo_id: string;
          updated_at: string;
          webhook_id: string | null;
        };
        Insert: {
          active?: boolean | null;
          created_at?: string;
          id?: string;
          project_id: string;
          repo_full_name: string;
          repo_id: string;
          updated_at?: string;
          webhook_id?: string | null;
        };
        Update: {
          active?: boolean | null;
          created_at?: string;
          id?: string;
          project_id?: string;
          repo_full_name?: string;
          repo_id?: string;
          updated_at?: string;
          webhook_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "project_github_integrations_project_id_fkey";
            columns: ["project_id"];
            isOneToOne: false;
            referencedRelation: "projects";
            referencedColumns: ["id"];
          }
        ];
      };
      project_jira_integrations: {
        Row: {
          active: boolean | null;
          created_at: string;
          id: string;
          jira_project_id: string;
          jira_project_key: string;
          project_id: string;
          updated_at: string;
          webhook_id: string | null;
        };
        Insert: {
          active?: boolean | null;
          created_at?: string;
          id?: string;
          jira_project_id: string;
          jira_project_key: string;
          project_id: string;
          updated_at?: string;
          webhook_id?: string | null;
        };
        Update: {
          active?: boolean | null;
          created_at?: string;
          id?: string;
          jira_project_id?: string;
          jira_project_key?: string;
          project_id?: string;
          updated_at?: string;
          webhook_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "project_jira_integrations_project_id_fkey";
            columns: ["project_id"];
            isOneToOne: true;
            referencedRelation: "projects";
            referencedColumns: ["id"];
          }
        ];
      };
      project_members: {
        Row: {
          created_at: string | null;
          employee_id: string;
          id: string;
          project_id: string;
          role: string;
        };
        Insert: {
          created_at?: string | null;
          employee_id: string;
          id?: string;
          project_id: string;
          role: string;
        };
        Update: {
          created_at?: string | null;
          employee_id?: string;
          id?: string;
          project_id?: string;
          role?: string;
        };
        Relationships: [
          {
            foreignKeyName: "project_members_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "project_members_project_id_fkey";
            columns: ["project_id"];
            isOneToOne: false;
            referencedRelation: "projects";
            referencedColumns: ["id"];
          }
        ];
      };
      projects: {
        Row: {
          client_name: string | null;
          created_at: string | null;
          description: string | null;
          end_date: string | null;
          github_repo: string | null;
          id: string;
          key: string | null;
          name: string;
          owner_id: string | null;
          project_type: string;
          slack_channel_id: string | null;
          start_date: string;
          status: string;
          updated_at: string | null;
        };
        Insert: {
          client_name?: string | null;
          created_at?: string | null;
          description?: string | null;
          end_date?: string | null;
          github_repo?: string | null;
          id?: string;
          key?: string | null;
          name: string;
          owner_id?: string | null;
          project_type?: string;
          slack_channel_id?: string | null;
          start_date: string;
          status: string;
          updated_at?: string | null;
        };
        Update: {
          client_name?: string | null;
          created_at?: string | null;
          description?: string | null;
          end_date?: string | null;
          github_repo?: string | null;
          id?: string;
          key?: string | null;
          name?: string;
          owner_id?: string | null;
          project_type?: string;
          slack_channel_id?: string | null;
          start_date?: string;
          status?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      skills: {
        Row: {
          category: string;
          created_at: string;
          id: string;
          name: string;
          updated_at: string;
        };
        Insert: {
          category: string;
          created_at?: string;
          id: string;
          name: string;
          updated_at?: string;
        };
        Update: {
          category?: string;
          created_at?: string;
          id?: string;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      tickets: {
        Row: {
          assignee_id: string | null;
          created_at: string;
          description: string;
          due_date: string | null;
          estimated_hours: number | null;
          github_prs: Json | null;
          id: string;
          jira_key: string | null;
          labels: string[];
          priority: string;
          project_id: string;
          reporter_id: string;
          status: string;
          title: string;
          type: string;
          updated_at: string;
        };
        Insert: {
          assignee_id?: string | null;
          created_at?: string;
          description: string;
          due_date?: string | null;
          estimated_hours?: number | null;
          github_prs?: Json | null;
          id?: string;
          jira_key?: string | null;
          labels?: string[];
          priority: string;
          project_id: string;
          reporter_id: string;
          status: string;
          title: string;
          type: string;
          updated_at?: string;
        };
        Update: {
          assignee_id?: string | null;
          created_at?: string;
          description?: string;
          due_date?: string | null;
          estimated_hours?: number | null;
          github_prs?: Json | null;
          id?: string;
          jira_key?: string | null;
          labels?: string[];
          priority?: string;
          project_id?: string;
          reporter_id?: string;
          status?: string;
          title?: string;
          type?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "tickets_assignee_id_fkey";
            columns: ["assignee_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tickets_project_id_fkey";
            columns: ["project_id"];
            isOneToOne: false;
            referencedRelation: "projects";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tickets_reporter_id_fkey";
            columns: ["reporter_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      users: {
        Row: {
          clerk_id: string | null;
          created_at: string | null;
          disabled: boolean;
          email: string;
          id: string;
          image_url: string | null;
          name: string;
          role: string;
          updated_at: string | null;
        };
        Insert: {
          clerk_id?: string | null;
          created_at?: string | null;
          disabled?: boolean;
          email: string;
          id: string;
          image_url?: string | null;
          name: string;
          role: string;
          updated_at?: string | null;
        };
        Update: {
          clerk_id?: string | null;
          created_at?: string | null;
          disabled?: boolean;
          email?: string;
          id?: string;
          image_url?: string | null;
          name?: string;
          role?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      note_type: "OBSERVATION" | "ACHIEVEMENT" | "NOTE";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      note_type: ["OBSERVATION", "ACHIEVEMENT", "NOTE"],
    },
  },
} as const;
