import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types.ts";

// Handle both Vite's import.meta.env and <PERSON>de's process.env
const getEnv = (key: string): string => {
  // For Node.js/CommonJS
  if (typeof process !== 'undefined' && process?.env?.[key]) {
    return process.env[key] as string;
  }
  
  // For Vite/ESM
  if (typeof import.meta !== 'undefined' && (import.meta as any)?.env?.[key]) {
    return (import.meta as any).env[key] as string;
  }
  
  throw new Error(`Missing required environment variable: ${key}`);
};

const SUPABASE_URL = getEnv('VITE_SUPABASE_URL');
const SUPABASE_PUBLISHABLE_KEY = getEnv('VITE_SUPABASE_ANON_KEY');

// Returns a Supabase client authenticated with the given JWT (Clerk session)
export function getSupabaseClient(token: string | null) {
  if (!SUPABASE_URL || !SUPABASE_PUBLISHABLE_KEY) {
    throw new Error("Supabase URL or anonymous key is not defined.");
  }
  return createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
    global: {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  });
}
