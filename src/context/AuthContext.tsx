import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { UserRole } from '@/types';
import { useSyncClerkUserToSupabase } from '@/hooks/useSyncClerkUserToSupabase';

interface AuthContextType {
  isAuthenticated: boolean;
  userRole: UserRole;
  supabaseUserId: string | null;
  isLoading: boolean; // Added isLoading property
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  userRole: 'developer',
  supabaseUserId: null,
  isLoading: true, // Added default value
});

export const useAuthContext = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { isLoaded, isSignedIn, user } = useUser();
  const { getToken } = useAuth();
  const [role, setRole] = useState<UserRole>('developer');
  const [supabaseUserId, setSupabaseUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Added loading state
  
  // Use the hook to sync Clerk user to Supabase
  useSyncClerkUserToSupabase();

  useEffect(() => {
    const fetchUserRole = async () => {
      if (!isSignedIn || !user) {
        setRole('developer');
        setSupabaseUserId(null);
        setIsLoading(false);
        return;
      }

      try {
        const token = await getToken();
        if (!token) {
          setIsLoading(false);
          return;
        }

        const { getSupabaseClient } = await import('@/integrations/supabase/client');
        const supabase = getSupabaseClient(token);
        
        const primaryEmail = user.primaryEmailAddress?.emailAddress;
        
        if (!primaryEmail) {
          console.error('No primary email found for user');
          setIsLoading(false);
          return;
        }
        
        const { data, error } = await supabase
          .from('users')
          .select('id, role, disabled')
          .eq('email', primaryEmail)
          .maybeSingle();
        
        if (error) {
          console.error('Error fetching user role', error);
          setIsLoading(false);
          return;
        }
        
        if (data) {
          if (data.disabled) {
            console.error('User is disabled');
            await getToken({ template: 'supabase' }); // to sign out
            window.location.href = '/';
            return;
          }
          setRole((data.role as UserRole) || 'developer');
          setSupabaseUserId(data.id);
        } else {
          console.warn('No user found with email:', primaryEmail);
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Error in fetchUserRole', error);
        setIsLoading(false);
      }
    };

    if (isLoaded) {
      fetchUserRole();
    } else {
      setIsLoading(true);
    }
  }, [isLoaded, isSignedIn, getToken, user]);

  const value = {
    isAuthenticated: isSignedIn || false,
    userRole: role,
    supabaseUserId,
    isLoading: !isLoaded || isLoading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
