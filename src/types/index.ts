export type UserRole = "admin" | "developer" | "client" | "project_manager";

export interface User {
  id: string;
  name: string;
  email: string;
  imageUrl: string;
  role: UserRole;
  clerkId?: string;
}

export type TicketStatus =
  | "backlog"
  | "todo"
  | "in-progress"
  | "review"
  | "done";
export type TicketPriority = "low" | "medium" | "high" | "urgent";
export type TicketType = "bug" | "feature" | "task" | "improvement";

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  image_url: string;
  clerk_id?: string;
}

export interface Comment {
  id: string;
  ticketId: string;
  userId: string | null;
  content: string;
  createdAt: string;
  updatedAt: string;
  user?: UserProfile | null;
  jiraCommentId?: string;
  authorName?: string;
}

export interface Ticket {
  id: string;
  title: string;
  description: string;
  comments: Comment[];
  status: TicketStatus;
  priority: TicketPriority;
  position: number;
  type: TicketType;
  // assigneeIds: UserProfile[];
  assignees: UserProfile[];
  reporter_id: string | null;
  reporter: UserProfile | null;
  projectId: string;
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  estimatedHours?: number;
  labels: string[];
  githubPRs: GitHubPR[];
  assignee: UserProfile | null;
  reference?: string;
  jiraId?: string;
  assigneeId?: string | null;
  project_id?: string;
}

export interface ProjectMember {
  id: string;
  user_id: string;
  employee_id?: string;
  project_id: string;
  role: UserRole;
  created_at: string;
  updated_at?: string;
  user?: UserProfile;
}
export interface Project {
  id: string;
  name: string;
  key: string;
  description: string;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  members: ProjectMember[]; 
  membersWithRoles?: ProjectMember[];
  jiraProjectId?: string;
  slackChannelId?: string;
  syncWithJira?: boolean;
  owner_id?: string;
  jira_project_id?: string;
  slack_channel_id?: string;
}

export interface GitHubPR {
  id: string;
  title: string;
  url: string;
  status: "open" | "closed" | "merged";
  createdAt: string;
  updatedAt: string;
}

export interface JiraIntegration {
  projectId: string;
  jiraProjectId: string;
  syncEnabled: boolean;
  lastSyncedAt: string;
}

export interface SlackIntegration {
  projectId: string;
  slackChannelId: string;
  notificationsEnabled: boolean;
  eventTypes: ("ticket_created" | "ticket_updated" | "ticket_closed")[];
}
