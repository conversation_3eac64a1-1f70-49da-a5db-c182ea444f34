import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { SignIn, SignUp, SignedOut } from "@clerk/clerk-react";
import { AuthProvider } from "@/context/AuthContext";

// Pages
import Dashboard from "./pages/Dashboard";
import Projects from "./pages/Projects";
import ProjectBoard from "./pages/ProjectBoard";
import NotFound from "./pages/NotFound";
import Index from "./pages/Index";
import AuthLayout from "./components/layouts/AuthLayout";
import RequireAuth from "./components/auth/RequireAuth";
import TicketDetail from "./pages/TicketDetail";
import Tickets from "./pages/Tickets";
import Settings from "./pages/Settings";
import Members from "./pages/Members";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />

            <Route
              path="/sign-in/*"
              element={
                <SignedOut>
                  <AuthLayout>
                    <SignIn routing="path" path="/sign-in" />
                  </AuthLayout>
                </SignedOut>
              }
            />

            <Route
              path="/sign-up/*"
              element={
                <SignedOut>
                  <AuthLayout>
                    <SignUp routing="path" path="/sign-up" />
                  </AuthLayout>
                </SignedOut>
              }
            />

            <Route
              path="/dashboard"
              element={
                <RequireAuth>
                  <Dashboard />
                </RequireAuth>
              }
            />

            <Route
              path="/projects"
              element={
                <RequireAuth>
                  <Projects />
                </RequireAuth>
              }
            />

            <Route
              path="/projects/:projectIdentifier"
              element={
                <RequireAuth>
                  <ProjectBoard />
                </RequireAuth>
              }
            />

            <Route
              path="/tickets"
              element={
                <RequireAuth>
                  <Tickets />
                </RequireAuth>
              }
            />

            <Route
              path="/tickets/:ticketId"
              element={
                <RequireAuth>
                  <TicketDetail />
                </RequireAuth>
              }
            />

            <Route
              path="/settings"
              element={
                <RequireAuth>
                  <Settings />
                </RequireAuth>
              }
            />

            <Route
              path="/settings/projects/:projectId"
              element={
                <RequireAuth>
                  <Settings />
                </RequireAuth>
              }
            />

            <Route
              path="/members"
              element={
                <RequireAuth>
                  <Members />
                </RequireAuth>
              }
            />

            <Route path="/404" element={<NotFound />} />
            <Route path="*" element={<Navigate to="/404" replace />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
