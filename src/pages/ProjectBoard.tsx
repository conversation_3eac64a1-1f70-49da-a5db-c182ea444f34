import { useEffect, useState, useMemo } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useProjectStore } from "../store/projectStore";
import { useTicketStore } from "../store/ticketStore";
import { useUserStore } from "../store/userStore";
import { useKanbanColumnStore } from "../store/useKanbanColumnStore";
import { useAuth } from "@clerk/clerk-react";
import { TicketPriority, TicketType, Project } from "../types";

type UserOption = {
  id: string;
  name: string;
  email?: string;
  avatarUrl?: string;
};
import {
  DndContext,
  DragOverlay,
  closestCorners,
  useSensor,
  useSensors,
  PointerSensor,
} from "@dnd-kit/core";
import KanbanBoard from "../components/tickets/KanbanBoard";
import TicketCard from "../components/tickets/TicketCard";
import AppLayout from "@/components/layouts/AppLayout";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import ProjectHeader from "@/components/projects/ProjectHeader";
import ProjectFilters from "@/components/projects/ProjectFilters";
import ProjectViewSelector from "@/components/projects/ProjectViewSelector";
import { ProjectList } from "@/components/projects/ProjectList";
import CreateTicketDialog from "@/components/projects/CreateTicketDialog";
import AddProjectMemberDialog from "@/components/projects/AddProjectMemberDialog";
import { useAuthContext } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { syncProjectKeyFromJira } from "@/lib/jiraSync";

const ProjectBoard = () => {
  const { projectIdentifier } = useParams<{ projectIdentifier: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("kanban");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [filterPriority, setFilterPriority] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeDragId, setActiveDragId] = useState<string | null>(null);
  const [isCreatingTicket, setIsCreatingTicket] = useState(false);
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [newTicket, setNewTicket] = useState({
    title: "",
    description: "",
    priority: "medium" as TicketPriority,
    type: "task" as TicketType,
    status: "",
    assigneeIds: [] as string[],
  });

  const {
    projects,
    fetchProjects,
    currentProject,
    setCurrentProject,
    addProjectMember,
    removeProjectMember,
  } = useProjectStore();
  const { tickets, fetchTickets, updateTicketStatus, createTicket } =
    useTicketStore();
  const { columns: kanbanColumns, fetchColumns: fetchKanbanColumns } =
    useKanbanColumnStore();
  const { users, fetchUsers } = useUserStore();
  const { getToken, userId: clerkUserId } = useAuth();
  const { supabaseUserId: currentUserDbUuid, userRole } = useAuthContext();

  useEffect(() => {
    const fetchAll = async () => {
      setIsLoading(true);
      try {
        const token = await getToken({
          template:
            "supabase",
        });
        const { getSupabaseClient } = await import(
          "@/integrations/supabase/client"
        );
        const supabase = getSupabaseClient(token);

        const projectsData = await fetchProjects(supabase);
        let project: Project | null = null;

        if (projectIdentifier) {
          project =
            projectsData.find((p) => p.id === projectIdentifier) ||
            projectsData.find(
              (p) => p.key?.toLowerCase() === projectIdentifier.toLowerCase()
            ) ||
            null;
        }

        if (project) {
          let isMember = false;
          if (
            project.members &&
            project.members.length > 0 &&
            currentUserDbUuid
          ) {
            if (typeof project.members[0] === "string") {
              isMember = (project.members as string[]).includes(
                currentUserDbUuid
              );
            } else {
              isMember = (project.members as any[]).some(
                (member) => member.id === currentUserDbUuid
              );
            }
          }

          if (userRole === "admin" || isMember) {
            setCurrentProject(project);
            await fetchTickets(project.id, supabase);
            await fetchKanbanColumns(project.id, supabase);
          } else {
            toast({
              title: "Access Denied",
              description: "You are not a member of this project.",
              variant: "destructive",
            });
            navigate("/projects");
          }
        } else {
          toast({
            title: "Project not found",
            description: "The requested project could not be found.",
            variant: "destructive",
          });
          if (projectsData.length > 0) navigate("/projects");
        }

        await fetchUsers(supabase);
      } catch (e) {
        toast({
          title: "Error",
          description: "Failed to load project data",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAll();
  }, [
    fetchProjects,
    fetchTickets,
    fetchUsers,
    getToken,
    fetchKanbanColumns,
    projectIdentifier,
    setCurrentProject,
    clerkUserId,
    toast,
    navigate,
  ]);

  useEffect(() => {
    if (isCreatingTicket) {
      const sorted = [...kanbanColumns].sort((a, b) => a.position - b.position);
      const validStatus = sorted.some((col) => col.status === newTicket.status);
      if (sorted.length > 0 && (!newTicket.status || !validStatus)) {
        setNewTicket((prev) => ({ ...prev, status: sorted[0].status }));
      }
    }
  }, [isCreatingTicket, kanbanColumns, newTicket.status]);

  const availableStatusesForDialog = useMemo(
    () => kanbanColumns.map((col) => ({ name: col.name, status: col.status })),
    [kanbanColumns]
  );

  const filteredTickets = useMemo(() => {
    return tickets.filter((ticket) => {
      if (currentProject && ticket.project_id !== currentProject.id)
        return false;
      if (filterStatus && ticket.status !== filterStatus) return false;
      if (filterPriority && ticket.priority !== filterPriority) return false;
      if (searchQuery) {
        const q = searchQuery.toLowerCase();
        return (
          ticket.title.toLowerCase().includes(q) ||
          ticket.description.toLowerCase().includes(q) ||
          ticket.referenceKey?.toLowerCase().includes(q)
        );
      }
      return true;
    });
  }, [tickets, currentProject, filterStatus, filterPriority, searchQuery]);

  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredTickets.length / itemsPerPage);
  const paginatedTickets = filteredTickets.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 8 } })
  );

  function isUUID(str: string) {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
      str
    );
  }

  const handleSyncProjectKey = async () => {
    if (!currentProject) return;
    try {
      setIsLoading(true);
      const token = await getToken({
        template:
          "supabase",
      });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);

      if (!isUUID(currentProject.id)) {
        throw new Error(`Invalid projectId: ${currentProject.id}`);
      }
      const key = await syncProjectKeyFromJira(supabase, currentProject.id);

      if (key) {
        toast({ title: "Project Key Synced", description: `Key: ${key}` });
        const result = await syncJiraTickets(supabase, currentProject.id, key);
        if (result.success) {
          toast({
            title: "Tickets Synced",
            description: `${result.count} tickets`,
          });
          await fetchTickets(currentProject.id, supabase);
        } else {
          toast({
            title: "Sync Failed",
            description: result.error || "JIRA sync failed",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Sync Failed",
          description: "No matching JIRA project found",
          variant: "destructive",
        });
      }
    } catch (e) {
      toast({
        title: "Error",
        description: "JIRA sync failed",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddProjectMember = async (
    userId: string,
    employeeId: string | null,
    role: string
  ) => {
    if (!currentProject) return;
    try {
      setIsLoading(true);
      const token = await getToken({
        template: "supabase",
      });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);

      await addProjectMember(
        currentProject.id,
        userId,
        employeeId,
        role,
        supabase
      );
      toast({
        title: "Member Added",
        description: "Successfully added project member.",
      });
    } catch (e) {
      toast({
        title: "Error",
        description: "Failed to add project member",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveProjectMember = async (userId: string) => {
    if (!currentProject) return;
    try {
      setIsLoading(true);
      const token = await getToken({
        template:
          "supabase",
      });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);

      await removeProjectMember(currentProject.id, userId, supabase);
      toast({
        title: "Member Removed",
        description: "User was removed from project.",
      });
    } catch (e) {
      toast({
        title: "Error",
        description: "Failed to remove project member",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTicket = async () => {
    if (!currentProject) return;
    try {
      setIsLoading(true);
      const token = await getToken({
        template: "supabase"
      });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);

      if (!currentUserDbUuid) return;

      await createTicket(
        {
          ...newTicket,
          reporter_id: currentUserDbUuid,
          project_id: currentProject.id,
          labels: [],
        },
        supabase
      );

      await fetchTickets(currentProject.id, supabase);
      setIsCreatingTicket(false);
      toast({
        title: "Ticket Created",
        description: "Successfully created ticket.",
      });
    } catch (e) {
      toast({
        title: "Error",
        description: "Ticket creation failed",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const assignableUsers = useMemo(() => {
    if (!currentProject?.members) return [];

    if (
      currentProject.members.length > 0 &&
      typeof currentProject.members[0] === "object" &&
      "id" in currentProject.members[0] &&
      "name" in currentProject.members[0]
    ) {
      return currentProject.members as UserOption[];
    }
    return currentProject.members
      .map((memberId: string) => {
        const user = users.find((u) => u.id === memberId);
        if (user) {
          return {
            id: user.id,
            name: user.name || user.email || user.id,
            email: user.email,
            avatarUrl: user.avatarUrl,
          };
        }
        return null;
      })
      .filter(Boolean) as UserOption[];
  }, [currentProject, users]);
  const currentProjectMembers = useMemo(
    () =>
      (currentProject?.members &&
        Array.isArray(currentProject.members) &&
        currentProject.members.every(
          (m) => typeof m === "object" && m !== null && "role" in m
        )
        ? currentProject.members
        : []) as ((typeof users)[number] & { role: string })[],
    [currentProject]
  );

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex justify-center items-center h-[80vh] px-4">
          <div className="space-y-4 w-full max-w-5xl">
            <div className="h-8 w-2/3 sm:w-1/3 bg-gray-200 animate-pulse rounded" />
            <div className="h-6 w-3/4 sm:w-1/2 bg-gray-200 animate-pulse rounded" />
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="h-24 sm:h-32 bg-gray-200 animate-pulse rounded-xl"
                />
              ))}
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-6 space-y-6 max-w-7xl">
        {projects.length > 0 && currentProject ? (
          <>
            {/* Sticky header + filters */}
            <div className="sticky top-0 bg-white z-10 pb-3 border-b space-y-3">
              <ProjectHeader
                project={currentProject}
                onSyncProjectKey={handleSyncProjectKey}
                onCreateTicket={() => setIsCreatingTicket(true)}
              >
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button
                    variant="secondary"
                    onClick={handleSyncProjectKey}
                    className="cursor-pointer transition-colors"
                  >
                    Sync Jira
                  </Button>
                  <Button onClick={() => setIsAddingMember(true)}>
                    Manage Members
                  </Button>
                </div>
              </ProjectHeader>

              <ProjectFilters
                searchQuery={searchQuery}
                onSearchChange={setSearchQuery}
                filterStatus={filterStatus}
                onStatusChange={setFilterStatus}
                filterPriority={filterPriority}
                onPriorityChange={setFilterPriority}
              />
            </div>

            {/* Tabs & Views */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-4">
              <ProjectViewSelector
                activeTab={activeTab}
                viewMode={viewMode}
                onTabChange={setActiveTab}
                onViewModeChange={setViewMode}
              />

              {/* Kanban view */}
              <TabsContent
                value="kanban"
                className="mt-4 h-[calc(100vh-300px)] overflow-hidden"
              >
                {filteredTickets.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-16 text-center space-y-4">
                    <p className="text-gray-500">No tickets found with current filters.</p>
                    <Button onClick={() => setIsCreatingTicket(true)}>
                      Create your first ticket
                    </Button>
                  </div>
                ) : (
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCorners}
                    onDragStart={(e) => setActiveDragId(e.active.id as string)}
                    onDragEnd={({ active, over }) => {
                      if (over && active.id !== over.id) {
                        updateTicketStatus(active.id as string, String(over.id));
                      }
                      setActiveDragId(null);
                    }}
                  >
                    <div className="h-full w-full overflow-x-auto">
                      <div className="flex gap-4 min-w-full sm:min-w-0">
                        <KanbanBoard tickets={filteredTickets} />
                      </div>
                    </div>
                    <DragOverlay>
                      {activeDragId && (
                        <TicketCard
                          ticket={
                            filteredTickets.find((t) => t.id === activeDragId)!
                          }
                          isDragging={true}
                        />
                      )}
                    </DragOverlay>
                  </DndContext>
                )}
              </TabsContent>

              {/* List view */}
              <TabsContent
                value="list"
                className="mt-4 h-[calc(100vh-300px)] overflow-hidden flex flex-col"
              >
                {paginatedTickets.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-16 text-center space-y-4">
                    <p className="text-gray-500">No tickets found with current filters.</p>
                    <Button onClick={() => setIsCreatingTicket(true)}>
                      Create your first ticket
                    </Button>
                  </div>
                ) : (
                  <>
                    <ProjectList
                      tickets={paginatedTickets}
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={setCurrentPage}
                    />
                  </>
                )}
              </TabsContent>
            </Tabs>

            {/* Dialogs */}
            <CreateTicketDialog
              isOpen={isCreatingTicket}
              onClose={() => setIsCreatingTicket(false)}
              ticket={newTicket}
              onTicketChange={(field, value) =>
                setNewTicket((prev) => ({ ...prev, [field]: value }))
              }
              onCreateTicket={handleCreateTicket}
              users={assignableUsers}
              availableStatuses={availableStatusesForDialog}
              className="max-w-lg rounded-2xl shadow-xl"
            />

            <AddProjectMemberDialog
              isOpen={isAddingMember}
              onClose={() => setIsAddingMember(false)}
              onAddMember={handleAddProjectMember}
              onRemoveMember={handleRemoveProjectMember}
              onUpdateRole={async (userId: string, newRole: string) => {
                if (!currentProject) return;
                try {
                  setIsLoading(true);
                  const token = await getToken({ template: "supabase" });
                  const { getSupabaseClient } = await import(
                    "@/integrations/supabase/client"
                  );
                  const supabase = getSupabaseClient(token);

                  if (typeof (addProjectMember as any)?.updateRole === "function") {
                    await (addProjectMember as any).updateRole(
                      currentProject.id,
                      userId,
                      newRole,
                      supabase
                    );
                  } else {
                    await handleRemoveProjectMember(userId);
                    await handleAddProjectMember(userId, newRole);
                  }
                  toast({
                    title: "Role Updated",
                    description: "Member role updated successfully.",
                  });
                } catch (e) {
                  toast({
                    title: "Error",
                    description: "Failed to update member role",
                    variant: "destructive",
                  });
                } finally {
                  setIsLoading(false);
                }
              }}
              availableUsers={users}
              currentProjectMembers={currentProjectMembers}
              className="max-w-lg rounded-2xl shadow-xl"
            />
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 space-y-4">
            <p className="text-gray-500 text-lg">No projects found.</p>
            <Button onClick={() => navigate("/projects")}>
              Create a Project
            </Button>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default ProjectBoard;