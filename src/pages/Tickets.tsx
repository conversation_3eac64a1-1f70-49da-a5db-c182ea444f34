import { useEffect, useState, useMemo } from "react";
import AppLayout from "@/components/layouts/AppLayout";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@clerk/clerk-react";
import { getSupabaseClient } from "@/integrations/supabase/client";
import { useTicketStore } from "@/store/ticketStore";
import { useProjectStore } from "@/store/projectStore";
import { Link } from "react-router-dom";
import { useAuthContext } from "@/context/AuthContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { PriorityBadge, TypeBadge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Ticket } from "@/types";

const ITEMS_PER_PAGE = 7;

const Tickets = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const { toast } = useToast();
  const { getToken } = useAuth();
  const { fetchTickets, tickets } = useTicketStore();
  const { fetchProjects, projects } = useProjectStore();
  const { supabaseUserId: currentUserDbUuid } = useAuthContext();

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        const token = await getToken({
          template: "supabase",
        });
        const supabase = getSupabaseClient(token);

        await fetchProjects(supabase);
        await fetchTickets(undefined, supabase);
      } catch (error) {
        console.error("Error loading tickets:", error);
        toast({
          title: "Error",
          description: "Failed to load tickets",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [fetchTickets, fetchProjects, getToken, toast]);

  // Filter tickets assigned to the current user
  const myTickets = useMemo(() => {
    if (!currentUserDbUuid) return [];
    
    return tickets.filter((ticket) => {
      // Check if the current user is in the assignees array
      if (ticket.assignees && Array.isArray(ticket.assignees) && ticket.assignees.length > 0) {
        return ticket.assignees.some((assignee) => {
          // Assignees should be user objects with an id property
          return assignee && assignee.id === currentUserDbUuid;
        });
      }
      return false;
    });
  }, [tickets, currentUserDbUuid]);

  // Get project name for a ticket
  const getProjectName = (ticket: Ticket) => {
    // Check both projectId and project_id since the data might use either
    const projectId = ticket.projectId || ticket.project_id;
    const project = projects.find((p) => p.id === projectId);
    return project?.name || "Unknown Project";
  };

  // Get project key for a ticket
  const getProjectKey = (ticket: Ticket) => {
    // Check both projectId and project_id since the data might use either
    const projectId = ticket.projectId || ticket.project_id;
    const project = projects.find((p) => p.id === projectId);
    return project?.key || "—";
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "backlog":
        return "bg-gray-100 text-gray-800 border-gray-300";
      case "todo":
        return "bg-blue-100 text-blue-800 border-blue-300";
      case "in-progress":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "review":
        return "bg-purple-100 text-purple-800 border-purple-300";
      case "done":
        return "bg-green-100 text-green-800 border-green-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  // Pagination calculations
  const totalPages = Math.max(1, Math.ceil(myTickets.length / ITEMS_PER_PAGE));
  
  // Ensure current page is valid
  const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages);
  const startIndex = (validCurrentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedTickets = myTickets.slice(startIndex, endIndex);

  // Reset to page 1 when tickets change (only if needed)
  useEffect(() => {
    if (myTickets.length > 0 && currentPage > Math.ceil(myTickets.length / ITEMS_PER_PAGE)) {
      setCurrentPage(1);
    }
  }, [myTickets.length, currentPage]);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handlePageClick = (page: number) => {
    setCurrentPage(page);
  };

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (validCurrentPage <= 3) {
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      } else if (validCurrentPage >= totalPages - 2) {
        pages.push(1);
        pages.push("...");
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        pages.push(1);
        pages.push("...");
        pages.push(validCurrentPage - 1);
        pages.push(validCurrentPage);
        pages.push(validCurrentPage + 1);
        pages.push("...");
        pages.push(totalPages);
      }
    }

    return pages;
  };

  return (
    <AppLayout>
      <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-6">
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800">
            My Tickets
          </h1>
          <p className="text-sm sm:text-base text-gray-600 mt-2">
            All tickets assigned to you across all projects
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="w-12 h-12 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>
          </div>
        ) : myTickets.length > 0 ? (
          <>
            {/* Desktop Table View */}
            <div className="hidden md:block bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="hover:bg-transparent border-gray-100">
                      <TableHead className="font-semibold text-gray-700 py-4 px-6">
                        Ticket
                      </TableHead>
                      <TableHead className="font-semibold text-gray-700 py-4 px-6">
                        Project
                      </TableHead>
                      <TableHead className="font-semibold text-gray-700 py-4 px-6">
                        Status
                      </TableHead>
                      <TableHead className="font-semibold text-gray-700 py-4 px-6">
                        Priority
                      </TableHead>
                      <TableHead className="font-semibold text-gray-700 py-4 px-6">
                        Type
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedTickets.map((ticket) => (
                      <TableRow
                        key={ticket.id}
                        className="group hover:bg-gray-50/80 transition-all duration-200 ease-in-out hover:shadow-sm border-gray-100 hover:border-gray-200"
                      >
                        <TableCell className="py-4 px-6">
                          <div className="flex flex-col">
                            <Link
                              to={`/tickets/${ticket.reference || ticket.id}`}
                              className="font-medium text-gray-900 hover:text-blue-600 transition-colors duration-200 group-hover:text-blue-700 focus:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-sm"
                            >
                              {ticket.title}
                            </Link>
                            <span className="text-xs text-gray-500 mt-0.5 font-mono">
                              {getProjectKey(ticket)}-
                              {ticket.id.slice(0, 8)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="py-4 px-6">
                          <Link
                            to={`/projects/${
                              getProjectKey(ticket) ||
                              ticket.projectId || ticket.project_id
                            }`}
                            className="text-sm text-gray-700 hover:text-blue-600 transition-colors duration-200"
                          >
                            {getProjectName(ticket)}
                          </Link>
                        </TableCell>
                        <TableCell className="py-4 px-6">
                          <Badge
                            variant="outline"
                            className={`capitalize ${getStatusColor(
                              ticket.status
                            )}`}
                          >
                            {ticket.status.replace("-", " ")}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-4 px-6">
                          <PriorityBadge priority={ticket.priority} />
                        </TableCell>
                        <TableCell className="py-4 px-6">
                          <TypeBadge type={ticket.type} />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-4">
              {paginatedTickets.map((ticket) => (
                <div
                  key={ticket.id}
                  className="bg-white border border-gray-200 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200"
                >
                  <Link
                    to={`/tickets/${ticket.reference || ticket.id}`}
                    className="block"
                  >
                    <div className="flex flex-col space-y-3">
                      {/* Title and Reference */}
                      <div>
                        <h3 className="font-medium text-gray-900 hover:text-blue-600 transition-colors">
                          {ticket.title}
                        </h3>
                        <span className="text-xs text-gray-500 font-mono">
                          {getProjectKey(ticket)}-
                          {ticket.id.slice(0, 8)}
                        </span>
                      </div>

                      {/* Project */}
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Project: </span>
                        <Link
                          to={`/projects/${
                            getProjectKey(ticket) || ticket.projectId || ticket.project_id
                          }`}
                          className="text-blue-600 hover:text-blue-700"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {getProjectName(ticket)}
                        </Link>
                      </div>

                      {/* Badges */}
                      <div className="flex flex-wrap gap-2">
                        <Badge
                          variant="outline"
                          className={`capitalize ${getStatusColor(
                            ticket.status
                          )}`}
                        >
                          {ticket.status.replace("-", " ")}
                        </Badge>
                        <PriorityBadge priority={ticket.priority} />
                        <TypeBadge type={ticket.type} />
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4 bg-white border border-gray-200 rounded-xl shadow-sm px-4 py-3">
                {/* Results info */}
                <div className="text-sm text-gray-600 order-2 sm:order-1">
                  Showing{" "}
                  <span className="font-medium text-gray-900">
                    {startIndex + 1}
                  </span>{" "}
                  to{" "}
                  <span className="font-medium text-gray-900">
                    {Math.min(endIndex, myTickets.length)}
                  </span>{" "}
                  of{" "}
                  <span className="font-medium text-gray-900">
                    {myTickets.length}
                  </span>{" "}
                  tickets
                </div>

                {/* Pagination buttons */}
                <div className="flex items-center gap-2 order-1 sm:order-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePreviousPage}
                    disabled={validCurrentPage === 1}
                    className="h-8 px-3"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span className="hidden sm:inline ml-1">Previous</span>
                  </Button>

                  {/* Page numbers */}
                  <div className="hidden sm:flex items-center gap-1">
                    {getPageNumbers().map((page, index) =>
                      page === "..." ? (
                        <span
                          key={`ellipsis-${index}`}
                          className="px-2 text-gray-400"
                        >
                          ...
                        </span>
                      ) : (
                        <Button
                          key={page}
                          variant={validCurrentPage === page ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageClick(page as number)}
                          className="h-8 w-8 p-0"
                        >
                          {page}
                        </Button>
                      )
                    )}
                  </div>

                  {/* Mobile page indicator */}
                  <div className="sm:hidden text-sm text-gray-600">
                    <span className="font-medium text-gray-900">
                      {validCurrentPage}
                    </span>
                    <span className="mx-1">/</span>
                    <span>{totalPages}</span>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextPage}
                    disabled={validCurrentPage === totalPages}
                    className="h-8 px-3"
                  >
                    <span className="hidden sm:inline mr-1">Next</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-8 sm:p-12">
            <div className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="w-8 h-8 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No tickets assigned
              </h3>
              <p className="text-sm sm:text-base text-gray-500 text-center max-w-sm">
                You don't have any tickets assigned to you yet. Tickets assigned
                to you will appear here.
              </p>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default Tickets;
