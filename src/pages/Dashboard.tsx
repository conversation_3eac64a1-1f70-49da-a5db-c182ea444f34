import { useEffect, useState, useMemo } from "react";
import AppLayout from "@/components/layouts/AppLayout";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@clerk/clerk-react";
import { getSupabaseClient } from "@/integrations/supabase/client";
import { useProjectStore } from "@/store/projectStore";
import { useTicketStore } from "@/store/ticketStore";
import { Link } from "react-router-dom";
import { useAuthContext } from "@/context/AuthContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

const Dashboard = () => {
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { getToken } = useAuth();
  const { fetchProjects, projects } = useProjectStore();
  const { fetchTickets, tickets } = useTicketStore();
  const { supabaseUserId: currentUserDbUuid, userRole } = useAuthContext();

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        const token = await getToken({
          template:
            "supabase",
        });
        const supabase = getSupabaseClient(token);

        await fetchProjects(supabase);
        await fetchTickets(undefined, supabase);
      } catch (error) {
        console.error("Error loading dashboard data:", error);
        toast({
          title: "Error",
          description: "Failed to load dashboard data",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [fetchProjects, fetchTickets, getToken, toast]);

  const visibleProjects = useMemo(() => {
    if (userRole === "admin") {
      return projects;
    }
    if (!currentUserDbUuid) return [];
    return projects.filter((project) => {
      if (!project.members || project.members.length === 0) return false;
      if (typeof project.members[0] === "string") {
        return (project.members as string[]).includes(currentUserDbUuid);
      }
      return (project.members as any[]).some(
        (member) => member.id === currentUserDbUuid
      );
    });
  }, [projects, currentUserDbUuid, userRole]);

  const visibleTickets = useMemo(() => {
    if (userRole === "admin") {
      return tickets;
    }
    const visibleProjectIds = new Set(visibleProjects.map((p) => p.id));
    return tickets.filter((ticket) => visibleProjectIds.has(ticket.projectId));
  }, [tickets, visibleProjects, userRole]);

  // Tickets assigned to the current user
  const myAssignedTickets = useMemo(() => {
    if (!currentUserDbUuid) return [];
    
    // Filter from all tickets, not just visibleTickets
    return tickets.filter((ticket) => {
      // Check if the current user is in the assignees array
      if (ticket.assignees && Array.isArray(ticket.assignees) && ticket.assignees.length > 0) {
        return ticket.assignees.some((assignee) => {
          // Assignees should be user objects with an id property
          return assignee && assignee.id === currentUserDbUuid;
        });
      }
      return false;
    });
  }, [tickets, currentUserDbUuid]);

  const ticketsByStatus = useMemo(
    () =>
      visibleTickets.reduce((acc, ticket) => {
        acc[ticket.status] = (acc[ticket.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    [visibleTickets]
  );

  const ticketsByPriority = useMemo(
    () =>
      visibleTickets.reduce((acc, ticket) => {
        acc[ticket.priority] = (acc[ticket.priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    [visibleTickets]
  );

  return (
    <AppLayout>
      <div className="container mx-auto p-6">
        <h1 className="text-4xl font-bold mb-8 text-gray-800">Dashboard</h1>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="w-12 h-12 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Project Summary */}
            <Link to="/projects">
            <SummaryCard title="Projects" value={visibleProjects.length} />
            </Link>

            {/* Tickets Summary */}
            <Link to="/tickets">
            <SummaryCard title="Tickets" value={myAssignedTickets.length} />
            </Link>
            
            {/* Ticket Status */}
            <div className="dashboard-card">
              <h2 className="dashboard-card-title">Ticket Status</h2>
              <StatusList
                data={ticketsByStatus}
                statuses={["backlog", "todo", "in-progress", "review", "done"]}
              />
            </div>

            {/* Recent Projects */}
            <div className="dashboard-card md:col-span-2 lg:col-span-2 bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="p-6 border-b border-gray-100">
                <h2 className="dashboard-card-title text-xl font-semibold text-gray-900 mb-1">
                  Recent Projects
                </h2>
                <p className="text-sm text-gray-500">
                  Your most recently accessed projects
                </p>
              </div>

              {visibleProjects.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="hover:bg-transparent border-gray-100">
                        <TableHead className="font-semibold text-gray-700 py-4 px-6">
                          Project Name
                        </TableHead>
                        <TableHead className="font-semibold text-gray-700 py-4 px-6">
                          Key
                        </TableHead>
                        <TableHead className="font-semibold text-gray-700 py-4 px-6">
                          Status
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {visibleProjects.slice(0, 5).map((project, index) => (
                        <TableRow
                          key={project.id}
                          className="group hover:bg-gray-50/80 transition-all duration-200 ease-in-out hover:shadow-sm border-gray-100 hover:border-gray-200"
                        >
                          <TableCell className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              {/* Project avatar/icon */}
                              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm font-semibold shadow-sm">
                                {project.name.charAt(0).toUpperCase()}
                              </div>
                              <div className="flex flex-col">
                                <Link
                                  to={`/projects/${project.key || project.id}`}
                                  className="font-medium text-gray-900 hover:text-blue-600 transition-colors duration-200 group-hover:text-blue-700 focus:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-sm"
                                >
                                  {project.name}
                                </Link>
                                {project.description && (
                                  <span className="text-xs text-gray-500 mt-0.5 truncate max-w-xs">
                                    {project.description}
                                  </span>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6">
                            <Badge
                              variant="outline"
                              className="font-mono text-xs bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 hover:border-gray-300 transition-colors duration-200"
                            >
                              {project.key || "—"}
                            </Badge>
                          </TableCell>
                          <TableCell className="py-4 px-6">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                              <Badge className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 transition-colors duration-200">
                                Active
                              </Badge>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 px-6">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg
                      className="w-8 h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No projects found
                  </h3>
                  <p className="text-gray-500 text-center max-w-sm">
                    You don't have access to any projects yet. Create a new
                    project or ask to be added to an existing one.
                  </p>
                </div>
              )}
            </div>

            {/* Priority Distribution */}
            <div className="dashboard-card">
              <h2 className="dashboard-card-title">Ticket Priority</h2>
              <StatusList
                data={ticketsByPriority}
                statuses={["low", "medium", "high", "urgent"]}
              />
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

// --- Subcomponents ---
const SummaryCard = ({ title, value }: { title: string; value: number }) => (
  <div className="dashboard-card group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 ease-out hover:scale-[1.02] active:scale-[0.98] cursor-pointer h-75">
    {/* Subtle gradient overlay */}
    <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

    {/* Content container */}
    <div className="relative px-8 py-12 flex flex-col items-center text-center space-y-4">
      {/* Title */}
      <h2 className="dashboard-card-title text-lg font-semibold text-gray-700 group-hover:text-gray-900 transition-colors duration-200 tracking-wide">
        {title}
      </h2>

      {/* Value with enhanced styling */}
      <div className="relative">
        <div className="text-4xl lg:text-5xl font-bold text-gray-800 group-hover:text-gray-900 transition-all duration-200 tabular-nums">
          {value}
        </div>
        {/* Subtle accent line */}
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out" />
      </div>

      {/* Description with improved typography */}
      <p className="text-sm font-medium text-gray-500 group-hover:text-gray-600 transition-colors duration-200 uppercase tracking-wider">
        Total {title}
      </p>

      {/* Optional decorative element */}
      <div className="absolute top-4 right-4 w-2 h-2 bg-blue-400 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 delay-100 transform translate-x-1 group-hover:translate-x-0" />
    </div>

    {/* Bottom accent border */}
    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-center" />
  </div>
);

const StatusList = ({
  data,
  statuses,
}: {
  data: Record<string, number>;
  statuses: string[];
}) => {
  // Find the status with the highest count for highlighting
  const maxCount = Math.max(...Object.values(data));
  const hasData = Object.values(data).some((count) => count > 0);

  return (
    <div className="space-y-1 mt-4">
      {statuses.map((status) => {
        const count = data[status] || 0;
        const isHighest = count === maxCount && count > 0;
        const hasCount = count > 0;

        return (
          <div
            key={status}
            className={`
              group relative flex items-center justify-between 
              px-3 py-2.5 rounded-lg border transition-all duration-200 ease-in-out
              cursor-default select-none
              ${
                hasCount
                  ? "border-gray-200 bg-white hover:bg-gray-50 hover:border-gray-300 hover:shadow-sm"
                  : "border-gray-100 bg-gray-50/50 hover:bg-gray-100/70"
              }
              ${
                isHighest
                  ? "ring-2 ring-blue-100 border-blue-200 bg-blue-50/50 hover:bg-blue-50"
                  : ""
              }
              focus-within:ring-2 focus-within:ring-blue-100 focus-within:border-blue-200
            `}
            role="listitem"
            tabIndex={0}
            aria-label={`${status.replace("-", " ")} status: ${count} items`}
          >
            {/* Status indicator dot */}
            <div className="flex items-center space-x-3">
              <div
                className={`
                  w-2 h-2 rounded-full transition-colors duration-200
                  ${
                    hasCount
                      ? isHighest
                        ? "bg-blue-500 group-hover:bg-blue-600"
                        : "bg-gray-400 group-hover:bg-gray-500"
                      : "bg-gray-300"
                  }
                `}
                aria-hidden="true"
              />

              {/* Status name */}
              <span
                className={`
                  font-medium text-sm transition-colors duration-200 capitalize
                  ${
                    hasCount
                      ? isHighest
                        ? "text-blue-900 group-hover:text-blue-950"
                        : "text-gray-700 group-hover:text-gray-900"
                      : "text-gray-500 group-hover:text-gray-600"
                  }
                `}
              >
                {status.replace("-", " ")}
              </span>
            </div>

            {/* Count badge */}
            <div className="flex items-center space-x-2">
              <span
                className={`
                  inline-flex items-center justify-center min-w-[1.5rem] h-6 
                  px-2 rounded-full text-xs font-semibold transition-all duration-200
                  ${
                    hasCount
                      ? isHighest
                        ? "bg-blue-100 text-blue-800 group-hover:bg-blue-200 group-hover:text-blue-900"
                        : "bg-gray-100 text-gray-700 group-hover:bg-gray-200 group-hover:text-gray-800"
                      : "bg-gray-100 text-gray-400 group-hover:bg-gray-200"
                  }
                `}
              >
                {count}
              </span>

              {/* Highest indicator */}
              {isHighest && hasData && (
                <div
                  className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"
                  aria-label="Highest count"
                  title="Highest count"
                />
              )}
            </div>

            {/* Subtle progress bar background */}
            {hasData && (
              <div
                className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-blue-200 to-blue-300 rounded-full transition-all duration-300 ease-out"
                style={{
                  width: `${(count / Math.max(maxCount, 1)) * 100}%`,
                  opacity: isHighest ? 0.8 : 0.4,
                }}
                aria-hidden="true"
              />
            )}
          </div>
        );
      })}

      {/* Empty state message */}
      {!hasData && (
        <div className="text-center py-4 text-gray-400 text-sm italic">
          No data available
        </div>
      )}
    </div>
  );
};

export default Dashboard;
