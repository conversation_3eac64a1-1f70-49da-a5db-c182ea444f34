import { useState, useEffect, useMemo } from "react";
import { useProjectStore } from "../store/projectStore";
import { useTicketStore } from "../store/ticketStore";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import AppLayout from "@/components/layouts/AppLayout";
import { Plus, Loader2 } from "lucide-react";
import { useAuth } from "@clerk/clerk-react";
import { getSupabaseClient } from "@/integrations/supabase/client";
import { useAuthContext } from "@/context/AuthContext";
import ProjectEditDialog from "@/components/projects/ProjectEditDialog";
import { useToast } from "@/hooks/use-toast";

const Projects = () => {
  const { projects, fetchProjects, createProject, isLoading } =
    useProjectStore();
  const { tickets } = useTicketStore();
  const { userRole, supabaseUserId: currentUserDbUuid } = useAuthContext();
  const { getToken, userId: clerkUserId } = useAuth();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [newProject, setNewProject] = useState({
    name: "",
    description: "",
    key: "",
  });

  useEffect(() => {
    async function loadProjects() {
      try {
        const token = await getToken({
          template:
            "supabase",
        });
        const supabase = getSupabaseClient(token);
        await fetchProjects(supabase);
      } catch (error) {
        console.error("Failed to load projects:", error);
        toast({
          title: "Error",
          description: "Failed to load projects",
          variant: "destructive",
        });
      }
    }
    loadProjects();
  }, [fetchProjects, getToken, toast]);

  const handleCreateProject = async () => {
    if (!newProject.name) {
      toast({
        title: "Error",
        description: "Project name is required",
        variant: "destructive",
      });
      return;
    }

    try {
      const token = await getToken({
        template:
          "supabase",
      });
      const supabase = getSupabaseClient(token);

      if (!clerkUserId) {
        toast({
          title: "Error",
          description: "User not authenticated.",
          variant: "destructive",
        });
        return;
      }

      const creatorDbUuid = currentUserDbUuid;

      if (!creatorDbUuid) {
        toast({
          title: "Error",
          description: "Could not identify project creator.",
          variant: "destructive",
        });
        return;
      }

      const projectData = {
        name: newProject.name,
        description: newProject.description,
        key: newProject.key.toUpperCase(),
      };
      await createProject(projectData, supabase, creatorDbUuid);
      setNewProject({ name: "", description: "", key: "" });
      setOpen(false);
      await fetchProjects(supabase);

      toast({
        title: "Success",
        description: "Project created successfully",
        variant: "success",
      });
    } catch (error) {
      console.error("Failed to create project:", error);
      toast({
        title: "Error",
        description: "Failed to create project",
        variant: "destructive",
      });
    }
  };

  const getProjectTicketCounts = (projectId: string) => {
    const projectTickets = tickets.filter((t) => t.projectId === projectId);
    const totalTickets = projectTickets.length;
    const completedTickets = projectTickets.filter(
      (t) => t.status === "done"
    ).length;
    return { totalTickets, completedTickets };
  };

  const visibleProjects = useMemo(() => {
    if (userRole === "admin") {
      return projects;
    }
    if (!currentUserDbUuid) {
      return [];
    }
    return projects.filter((project) => {
      if (!project.members || project.members.length === 0) {
        return false;
      }

      return project.members.some((member) => {
        if (typeof member === "string") {
          return member === currentUserDbUuid;
        }
        return member.id === currentUserDbUuid;
      });
    });
  }, [projects, userRole, currentUserDbUuid]);

  if (isLoading) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6 flex items-center justify-center h-[80vh]">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-blue-500 border-dashed rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-500 font-medium">Loading projects...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800">Projects</h1>
          {userRole === "admin" && clerkUserId && (
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  New Project
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-white border border-gray-200 rounded-xl shadow-xl">
                <DialogHeader className="border-b border-gray-100 pb-4">
                  <DialogTitle className="text-xl font-semibold text-gray-900">
                    Create new project
                  </DialogTitle>
                  <DialogDescription className="text-gray-600">
                    Add a new project to your workspace.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-6 py-6">
                  <div className="grid gap-3">
                    <Label
                      htmlFor="name"
                      className="text-sm font-semibold text-gray-700"
                    >
                      Project Name
                    </Label>
                    <Input
                      id="name"
                      placeholder="My awesome project"
                      value={newProject.name}
                      onChange={(e) =>
                        setNewProject({ ...newProject, name: e.target.value })
                      }
                      className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
                    />
                  </div>
                  <div className="grid gap-3">
                    <Label
                      htmlFor="key"
                      className="text-sm font-semibold text-gray-700"
                    >
                      Project Key
                    </Label>
                    <Input
                      id="key"
                      placeholder="PRJ"
                      value={newProject.key}
                      onChange={(e) =>
                        setNewProject({
                          ...newProject,
                          key: e.target.value.toUpperCase(),
                        })
                      }
                      maxLength={5}
                      className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg font-mono"
                    />
                    <p className="text-sm text-gray-500">
                      A short, unique identifier for this project (max 5
                      characters).
                    </p>
                  </div>
                  <div className="grid gap-3">
                    <Label
                      htmlFor="description"
                      className="text-sm font-semibold text-gray-700"
                    >
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      placeholder="Describe your project"
                      value={newProject.description}
                      onChange={(e) =>
                        setNewProject({
                          ...newProject,
                          description: e.target.value,
                        })
                      }
                      className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg min-h-[100px]"
                    />
                  </div>
                </div>
                <DialogFooter className="border-t border-gray-100 pt-4 flex gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setOpen(false)}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateProject}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-semibold transition-colors duration-200"
                  >
                    Create Project
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {visibleProjects.length > 0 ? (
            visibleProjects.map((project) => {
              const { totalTickets, completedTickets } = getProjectTicketCounts(
                project.id
              );
              return (
                 <Link
                      to={`/projects/${project.key || project.id}`}
                      className="w-full"
                  >
                <Card
                  key={project.id}
                  className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 ease-out hover:scale-[1.02] flex flex-col"
                >
                  {/* Subtle gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <CardHeader className="relative p-6 border-b border-gray-100">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3 mb-2">
                          {/* Project avatar */}
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm font-bold shadow-sm">
                            {project.name.charAt(0).toUpperCase()}
                          </div>
                          <div className="flex-1 min-w-0">
                            <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-gray-950 transition-colors duration-200 truncate">
                              {project.name}
                            </CardTitle>
                          </div>
                        </div>
                        <CardDescription className="text-gray-600 group-hover:text-gray-700 transition-colors duration-200 line-clamp-2">
                          {project.description || "No description provided"}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                          <Badge className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 transition-colors duration-200 text-xs font-medium">
                            Active
                          </Badge>
                        </div>
                        {userRole === "admin" && (
                          <ProjectEditDialog
                            project={project}
                            onEdited={async () => {
                              const token = await getToken({
                                template: "supabase",
                              });
                              const supabase = getSupabaseClient(token);
                              await fetchProjects(supabase);
                            }}
                            onDeleted={async () => {
                              const token = await getToken({
                                template:
                                  import.meta.env.MODE === "production"
                                    ? "supabase"
                                    : "supabase",
                              });
                              const supabase = getSupabaseClient(token);
                              await fetchProjects(supabase);
                            }}
                          />
                        )}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="relative flex-grow p-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-500">
                          Project Key
                        </span>
                        <Badge
                          variant="outline"
                          className="font-mono text-xs bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 hover:border-gray-300 transition-colors duration-200"
                        >
                          {project.key || "—"}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-500">
                          Tickets
                        </span>
                        <span className="text-sm font-semibold text-gray-700">
                          {completedTickets} / {totalTickets} completed
                        </span>
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Progress
                          </span>
                          <span className="text-xs font-semibold text-gray-600">
                            {totalTickets > 0
                              ? Math.round(
                                  (completedTickets / totalTickets) * 100
                                )
                              : 0}
                            %
                          </span>
                        </div>
                        <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-500 ease-out"
                            style={{
                              width: `${
                                totalTickets > 0
                                  ? (completedTickets / totalTickets) * 100
                                  : 0
                              }%`,
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>

                  <CardFooter className="relative p-6 pt-0">
                      <Button
                        variant="outline"
                        className="w-full cursor-pointer border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 hover:text-gray-900 transition-all duration-200 font-medium"
                      >
                        View Project
                      </Button>
                  </CardFooter>

                  {/* Bottom accent border */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-center" />
                </Card>
              </Link>
              );
            })
          ) : (
            <Card className="col-span-1 md:col-span-2 lg:col-span-3 bg-white border border-gray-200 rounded-xl shadow-sm">
              <CardHeader className="text-center py-12 px-6">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg
                    className="w-8 h-8 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    />
                  </svg>
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900 mb-3">
                  No projects found
                </CardTitle>
                <CardDescription className="text-gray-600 max-w-md mx-auto">
                  {userRole === "admin"
                    ? "Get started by creating your first project to organize your work and collaborate with your team."
                    : "You are not a member of any projects yet. Contact an administrator to be added to one."}
                </CardDescription>
              </CardHeader>
              {userRole === "admin" && (
                <CardContent className="flex justify-center pb-12">
                  <Button
                    onClick={() => setOpen(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Create Project
                  </Button>
                </CardContent>
              )}
            </Card>
          )}
        </div>
      </div>
    </AppLayout>
  );
};

export default Projects;