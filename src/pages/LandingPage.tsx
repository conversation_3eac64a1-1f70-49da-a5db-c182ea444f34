
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import KanbanPreview from "../components/KanbanPreview"; // Import the new component

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-slate-50">
      <header className="container mx-auto flex items-center justify-between p-4">
        <div className="text-xl font-bold">ProjectFlow</div>
        <div className="space-x-4">
          <SignedIn>
            <Link to="/dashboard">
              <Button variant="default">Go to Dashboard</Button>
            </Link>
          </SignedIn>
          <SignedOut>
            <Link to="/sign-in">
              <Button variant="outline">Sign In</Button>
            </Link>
            <Link to="/sign-up">
              <Button>Sign Up</Button>
            </Link>
          </SignedOut>
        </div>
      </header>

      <main className="container mx-auto px-4 py-16 md:py-24">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
            Project Management, <span className="text-primary">Simplified</span>
          </h1>
          <p className="mt-6 text-lg text-gray-600">
            ProjectFlow streamlines your workflow with a powerful yet simple
            kanban board. Organize tasks, track progress, and collaborate
            seamlessly with your team.
          </p>
          <div className="mt-10 flex justify-center gap-4">
            <SignedIn>
              <Link to="/dashboard">
                <Button size="lg">Go to Dashboard</Button>
              </Link>
            </SignedIn>
            <SignedOut>
              <Link to="/sign-up">
                <Button size="lg">Get Started</Button>
              </Link>
              <Link to="/sign-in">
                <Button variant="outline" size="lg">
                  Learn More
                </Button>
              </Link>
            </SignedOut>
          </div>
        </div>

        <div className="mt-20 rounded-lg overflow-hidden shadow-xl">
          <KanbanPreview />
        </div>
      </main>
    </div>
  );
};

export default LandingPage;
