import { useUserStore } from "@/store/userStore";
import { useProjectStore } from "@/store/projectStore";
import { useState, useEffect } from "react";
import AppLayout from "@/components/layouts/AppLayout";
import { Button } from "@/components/ui/button";
import { useAuth } from "@clerk/clerk-react";
import { Loader2, Users, UserPlus, Settings } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { getSupabaseClient } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuthContext } from "@/context/AuthContext";

const Members = () => {
  const { users, fetchUsers, isLoading, error } = useUserStore();
  const {
    projects,
    currentProject,
    addMemberToProject,
    removeMemberFromProject,
    fetchProjects,
  } = useProjectStore();
  const [selectedProjectId, setSelectedProjectId] = useState(
    currentProject?.id || ""
  );
  const { getToken } = useAuth();
  const { userRole } = useAuthContext();
  const [isInitializing, setIsInitializing] = useState(true);
  const { toast } = useToast();

  const supabase = getSupabaseClient(null);

  useEffect(() => {
    const initializeData = async () => {
      try {
        setIsInitializing(true);
        const token = await getToken({
          template:
            "supabase",
        });
        const { getSupabaseClient } = await import(
          "@/integrations/supabase/client"
        );
        const supabase = getSupabaseClient(token);

        await fetchUsers(supabase);
        await fetchProjects(supabase);
      } catch (err) {
        console.error("Failed to initialize Members page:", err);
        toast({
          title: "Error",
          description: "Failed to load members data",
          variant: "destructive",
        });
      } finally {
        setIsInitializing(false);
      }
    };

    initializeData();
  }, [getToken, fetchUsers, fetchProjects, toast]);

  const project = projects.find((p) => p.id === selectedProjectId);

  const handleDeleteUser = async (userId: string) => {
    try {
      const token = await getToken({
        template:
          "supabase",
      });
      const supabase = getSupabaseClient(token);

      // First, delete all project memberships for the user
      const { error: memberError } = await supabase
        .from("project_members")
        .delete()
        .eq("user_id", userId);

      if (memberError) throw memberError;

      // Then, delete the user
      const { error: userError } = await supabase
        .from("users")
        .delete()
        .eq("id", userId);

      if (userError) throw userError;

      await fetchUsers(supabase);
      toast({
        title: "Success",
        description: "User deleted successfully",
        variant: "success",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: `Failed to delete user: ${err}`,
        variant: "destructive",
      });
    }
  };

  const handleToggleDisableUser = async (userId: string, isDisabled: boolean) => {
    try {
      const token = await getToken({
        template:
          "supabase",
      });
      const supabase = getSupabaseClient(token);
      const { error } = await supabase
        .from("users")
        .update({ disabled: !isDisabled })
        .eq("id", userId);
      if (error) throw error;
      await fetchUsers(supabase);
      toast({
        title: "Success",
        description: `User ${!isDisabled ? "disabled" : "enabled"} successfully`,
        variant: "success",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: `Failed to ${!isDisabled ? "disable" : "enable"} user: ${err}`,
        variant: "destructive",
      });
    }
  };

  if (isInitializing) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6 flex items-center justify-center h-[80vh]">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-blue-500 border-dashed rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-500 font-medium">Loading members...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-8">
        <h1 className="text-4xl font-bold mb-8 text-gray-800">
          Members Management
        </h1>

        {/* Add New User Section */}
        <section className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
          {/* Subtle gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          <div className="relative p-6 border-b border-gray-100">
            <div className="flex items-center gap-3 mb-1">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                <UserPlus className="h-4 w-4" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                Add New User
              </h2>
            </div>
            <p className="text-sm text-gray-500">
              Create a new user account in the system
            </p>
          </div>

          <div className="relative p-6">
            <form
              onSubmit={async (e) => {
                e.preventDefault();
                const form = e.target as HTMLFormElement;
                const name = (
                  form.elements.namedItem("name") as HTMLInputElement
                ).value;
                const email = (
                  form.elements.namedItem("email") as HTMLInputElement
                ).value;
                const role = (
                  form.elements.namedItem("role") as HTMLSelectElement
                ).value;

                if (!name || !email || !role) {
                  toast({
                    title: "Error",
                    description: "All fields are required",
                    variant: "destructive",
                  });
                  return;
                }

                try {
                  const token = await getToken({
                    template:
                      import.meta.env.MODE === "production"
                        ? "supabase"
                        : "supabase",
                  });
                  const { getSupabaseClient } = await import(
                    "@/integrations/supabase/client"
                  );
                  const supabase = getSupabaseClient(token);
                  const { error } = await supabase.from("users").insert([
                    {
                      id: crypto.randomUUID(),
                      name,
                      email,
                      role,
                      created_at: new Date().toISOString(),
                    },
                  ]);
                  if (error) throw error;
                  await fetchUsers(supabase);
                  form.reset();
                  toast({
                    title: "Success",
                    description: "User created successfully",
                    variant: "success",
                  });
                } catch (err) {
                  toast({
                    title: "Error",
                    description: `Failed to add user: ${err}`,
                    variant: "destructive",
                  });
                }
              }}
              className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end"
            >
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">
                  Name
                </label>
                <input
                  name="name"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none transition-colors duration-200"
                  placeholder="Enter full name"
                  required
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">
                  Email
                </label>
                <input
                  name="email"
                  type="email"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none transition-colors duration-200"
                  placeholder="Enter email address"
                  required
                />
              </div>
              <div className="relative w-full">
                <select
                  name="role"
                  required
                  defaultValue="developer"
                  className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 pr-10 text-sm bg-white text-gray-800 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none transition-all duration-200 box-border"
                >
                  <option value="member">Member</option>
                  <option value="developer">Developer</option>
                  <option value="admin">Admin</option>
                </select>

                {/* Custom dropdown arrow */}
                <div className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </div>

              <Button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 flex items-center gap-2"
              >
                <UserPlus className="h-4 w-4" />
                Add User
              </Button>
            </form>
          </div>
        </section>

        {/* All Application Members Section */}
        <section className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
          {/* Subtle gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          <div className="relative p-6 border-b border-gray-100">
            <div className="flex items-center gap-3 mb-1">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                <Users className="h-4 w-4" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                All Application Members
              </h2>
            </div>
            <p className="text-sm text-gray-500">
              Manage all users in the system
            </p>
          </div>

          <div className="relative p-6">
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 font-medium">
                  Error loading users: {error}
                </p>
              </div>
            )}

            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-blue-500 border-dashed rounded-full animate-spin mx-auto mb-3"></div>
                  <p className="text-gray-500 text-sm">Loading members...</p>
                </div>
              </div>
            ) : users.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Users className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No members found
                </h3>
                <p className="text-gray-500">No members found in the system.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Name
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Email
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Role
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {users.map((user) => (
                      <tr
                        key={user.id}
                        className={`group hover:bg-gray-50/80 transition-all duration-200 ease-in-out hover:shadow-sm ${
                          user.disabled ? "bg-gray-100" : ""
                        }`}
                      >
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-3">
                            <div
                              className={`w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center text-white text-sm font-semibold shadow-sm ${
                                user.disabled ? "opacity-50" : ""
                              }`}
                            >
                              {user.name?.charAt(0).toUpperCase() ||
                                user.email?.charAt(0).toUpperCase()}
                            </div>
                            <span
                              className={`font-medium text-gray-900 group-hover:text-gray-950 transition-colors duration-200 ${
                                user.disabled ? "line-through text-gray-500" : ""
                              }`}
                            >
                              {user.name}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span
                            className={`text-gray-600 group-hover:text-gray-700 transition-colors duration-200 ${
                              user.disabled ? "line-through text-gray-500" : ""
                            }`}
                          >
                            {user.email}
                          </span>
                        </td>
                        <td className="px-6 py-4 space-x-2">
                          <Badge
                            variant="outline"
                            className="bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 hover:border-gray-300 transition-colors duration-200 capitalize"
                          >
                            {user.role}
                          </Badge>
                          {user.disabled && (
                            <Badge variant="destructive">Disabled</Badge>
                          )}
                        </td>
                        <td className="px-6 py-4 space-x-2">
                          {userRole === "admin" && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleToggleDisableUser(user.id, user.disabled)}
                              >
                                {user.disabled ? "Enable" : "Disable"}
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleDeleteUser(user.id)}
                              >
                                Delete
                              </Button>
                            </>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </section>
      </div>
    </AppLayout>
  );
};

export default Members;
