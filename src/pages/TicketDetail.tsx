import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  ChevronLeft,
  Clock,
  GitPullRequest,
  AlignLeft,
  MessageSquare,
  Link2,
  CircleDot,
  Calendar,
  Send,
  Edit,
  UserRound,
} from "lucide-react";
import Avatar from "../components/ui/avatar";
import { Button } from "@/components/ui/button";
import Select from "@/components/ui/select";
import { PriorityBadge, StatusBadge, TypeBadge } from "../components/ui/badge";
import { useTicketStore } from "../store/ticketStore";
import type { TicketStatus } from "../types";
import { useUserStore } from "../store/userStore";
import { useProjectStore } from "../store/projectStore";
import { useAuth, useUser } from "@clerk/clerk-react";
import { useToast } from "@/hooks/use-toast";
import { format, parseISO } from "date-fns";
import { useNavigate } from "react-router-dom";

const TicketDetail = () => {
  const { ticketId } = useParams<{ ticketId: string }>();
  const {
    setCurrentTicket,
    currentTicket,
    updateTicket,
    updateTicketStatus,
    comments,
    createComment,
    fetchComments,
  } = useTicketStore();
  const navigate = useNavigate();
  const { users, getCurrentUserUuid } = useUserStore();
  const { tickets, fetchTickets } = useTicketStore();
  const { getToken, userId, isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const { projects } = useProjectStore();
  const [isEditing, setIsEditing] = useState(false);
  const [description, setDescription] = useState("");
  const [newComment, setNewComment] = useState("");
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  interface Relationship {
    id: string;
    relationship_type: string;
    related: {
      id: string;
      title: string;
      status: string;
    };
  }

  const [dependencies, setDependencies] = useState<Relationship[]>([]);
  const [blockedBy, setBlockedBy] = useState<Relationship[]>([]);
  const [showAddRelation, setShowAddRelation] = useState(false);
  const [selectedRelatedTicket, setSelectedRelatedTicket] =
    useState<string>("");
  const [selectedRelationType, setSelectedRelationType] =
    useState<string>("relates to");
  const { toast } = useToast();
  const { fetchUsers } = useUserStore();
  const { fetchProjects } = useProjectStore();

  const fetchTicketRelationships = async () => {
    if (!ticketId) return;
    try {
      const token = await getToken({
        template: "supabase",
      });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);

      const { data: outgoing } = await supabase
        .from("ticket_dependencies")
        .select(
          `
          id,
          related_ticket_id,
          relationship_type,
          related: tickets!ticket_dependencies_related_ticket_id_fkey (
            id, title, status
          )
        `
        )
        .eq("ticket_id", ticketId);

      const { data: incoming } = await supabase
        .from("ticket_dependencies")
        .select(
          `
          id,
          ticket_id,
          relationship_type,
          ticket: tickets!ticket_dependencies_ticket_id_fkey (
            id, title, status
          )
        `
        )
        .eq("related_ticket_id", ticketId);

      setDependencies(outgoing || []);
      setBlockedBy(incoming || []);
    } catch (e) {
      console.error("Failed to fetch relationships", e);
      toast({
        title: "Error",
        description: "Failed to load ticket relationships.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    const loadUsers = async () => {
      if (isSignedIn && users.length === 0) {
        try {
          const token = await getToken({
            template:
              import.meta.env.MODE === "production" ? "supabase" : "supabase",
          });
          const { getSupabaseClient } = await import(
            "@/integrations/supabase/client"
          );
          const supabase = getSupabaseClient(token);
          await fetchUsers(supabase);
        } catch (error) {
          console.error("Failed to fetch users in TicketDetail:", error);
          toast({
            title: "Error",
            description: "Failed to load user data.",
            variant: "destructive",
          });
        }
      }
    };
    loadUsers();
  }, [isSignedIn, getToken, fetchUsers, users.length, toast]);

  useEffect(() => {
    const loadProjects = async () => {
      if (isSignedIn && projects.length === 0) {
        try {
          const token = await getToken({
            template:
              import.meta.env.MODE === "production" ? "supabase" : "supabase",
          });
          const { getSupabaseClient } = await import(
            "@/integrations/supabase/client"
          );
          const supabase = getSupabaseClient(token);
          await fetchProjects(supabase);
        } catch (error) {
          console.error("Failed to fetch projects in TicketDetail:", error);
          toast({
            title: "Error",
            description: "Failed to load project data.",
            variant: "destructive",
          });
        }
      }
    };
    loadProjects();
  }, [isSignedIn, getToken, fetchProjects, projects.length, toast]);

  useEffect(() => {
    if (ticketId) {
      const fetchTicketAndComments = async () => {
        try {
          const token = await getToken({
            template:
              import.meta.env.MODE === "production" ? "supabase" : "supabase",
          });
          const { getSupabaseClient } = await import(
            "@/integrations/supabase/client"
          );
          const supabase = getSupabaseClient(token);

          const isUUID =
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
              ticketId
            );

          let ticket;
          if (isUUID) {
            const { data } = await supabase
              .from("tickets")
              .select("*")
              .eq("id", ticketId)
              .single();
            ticket = data;
          } else {
            const { data } = await supabase
              .from("tickets")
              .select("*")
              .eq("reference", ticketId)
              .single();
            ticket = data;
          }

          if (!ticket) {
            toast({
              title: "Not found",
              description: "Ticket not found.",
              variant: "destructive",
            });
            navigate("/projects");
            return;
          }

          // Check if user is a member of the project
          if (userId && user) {
            const email = user.primaryEmailAddress?.emailAddress || "";
            const currentUserDbId = getCurrentUserUuid(userId, email);

            if (!currentUserDbId) {
              toast({
                title: "Error",
                description: "Could not verify your access. Please try again.",
                variant: "destructive",
              });
              navigate("/projects");
              return;
            }
            const { data: membership } = await supabase
              .from("project_members")
              .select("id")
              .eq("project_id", ticket.project_id)
              .eq("user_id", currentUserDbId)
              .maybeSingle();

            if (!membership) {
              toast({
                title: "Access Denied",
                description: "You do not have permission to view this ticket.",
                variant: "destructive",
              });
              navigate("/projects");
              return;
            }
          }

          if (!userId || !user) {
            toast({
              title: "Authentication Required",
              description: "Please sign in to view this ticket.",
              variant: "destructive",
            });
            navigate("/projects");
            return;
          }

 // Check if user is a member of the project
          const email = user.primaryEmailAddress?.emailAddress || '';
          const currentUserDbId = getCurrentUserUuid(userId, email);
          
          if (!currentUserDbId) {
            toast({
              title: "Access Denied",
             description: "Could not verify your identity.",
              variant: "destructive",
           });
            navigate("/projects");
            return;
          }

            const { data: membership } = await supabase
            .from("project_members")
            .select("id")
            .eq("project_id", ticket.project_id)
            .eq("user_id", currentUserDbId)
            .maybeSingle();

          if (!membership) {
            toast({
              title: "Access Denied",
              description: "You do not have permission to view this ticket.",
              variant: "destructive",
            });
            navigate("/projects");
            return;
          }
      

          await fetchTickets(ticket.project_id, supabase);
          setCurrentTicket(ticket.id);

          await fetchComments(ticket.id, supabase);
        } catch (e) {
          console.error("Failed to get Supabase client or fetch ticket", e);
          toast({
            title: "Error",
            description: "Failed to load ticket details.",
            variant: "destructive",
          });
        }
      };
      fetchTicketAndComments();
      fetchTicketRelationships();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    ticketId,
    tickets.length,
    fetchTickets,
    setCurrentTicket,
    getToken,
    toast,
    isSignedIn,
    fetchComments,
    getCurrentUserUuid,
    navigate,
    userId,
  ]);

  useEffect(() => {
    if (ticketId && tickets.length === 0) {
      const fetchTicketAndProject = async () => {
        const token = await getToken({
          template: "supabase",
        });
        const { getSupabaseClient } = await import(
          "@/integrations/supabase/client"
        );
        const supabase = getSupabaseClient(token);

        const { data: ticket, error } = await supabase
          .from("tickets")
          .select("id, project_id")
          .eq("id", ticketId)
          .single();

        if (ticket && ticket.project_id) {
          await fetchTickets(ticket.project_id, supabase);
          setCurrentTicket(ticketId);
        }
      };
      fetchTicketAndProject();
    } else if (ticketId && tickets.length > 0) {
      setCurrentTicket(ticketId);
    }
  }, [ticketId, tickets.length, fetchTickets, setCurrentTicket, getToken]);

  useEffect(() => {
    if (currentTicket) {
      setDescription(currentTicket.description);
    }
  }, [currentTicket]);

  if (!currentTicket) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-gray-500">Loading ticket...</p>
      </div>
    );
  }

  const ticketAssignees = currentTicket.assignees || [];
  const reporter = users.find((u) => u.id === currentTicket.reporterId);
  const project = projects.find((p) => p.id === currentTicket.projectId);

  const handleUpdateTicket = async (updates: Partial<typeof currentTicket>) => {
    try {
      const token = await getToken({
        template: "supabase",
      });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);
      await updateTicket(currentTicket.id, updates, supabase);
      toast({
        title: "Success",
        description: "Ticket updated.",
        variant: "success",
      });
    } catch (e) {
      console.error("Failed to update ticket", e);
      toast({
        title: "Error",
        description: "Failed to update ticket.",
        variant: "destructive",
      });
    }
  };

  const handleSaveDescription = async () => {
    await handleUpdateTicket({ description });
    setIsEditing(false);
  };

  const handleChangeStatus = async (newStatus: string) => {
    if (!currentTicket) return;
    try {
      const token = await getToken({ template: "supabase" });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);

      // Use the store helper to update status and local store
      if (updateTicketStatus) {
        await updateTicketStatus(currentTicket.id, newStatus as TicketStatus, supabase);
        toast({ title: "Success", description: "Ticket status updated." });
      } else {
        // Fallback to generic update if specific fn not available
        await updateTicket(currentTicket.id, { status: newStatus }, supabase);
        toast({ title: "Success", description: "Ticket status updated." });
      }
    } catch (e) {
      console.error("Failed to update ticket status", e);
      toast({ title: "Error", description: "Failed to update ticket status.", variant: "destructive" });
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim() || !userId || !ticketId) return;

    try {
      const token = await getToken({
        template: "supabase",
      });
      const { getSupabaseClient } = await import(
        "@/integrations/supabase/client"
      );
      const supabase = getSupabaseClient(token);
      const email = user?.primaryEmailAddress?.emailAddress || "";
      const currentUserUuid = getCurrentUserUuid(userId || "", email);
      if (!currentUserUuid) throw new Error("Could not find user profile.");
      await createComment(
        ticketId,
        currentUserUuid,
        newComment.trim(),
        supabase
      );
      setNewComment("");
    } catch (e) {
      console.error("Failed to add comment", e);
      toast({
        title: "Error",
        description: "Failed to add comment.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate(-1)}
          className="flex items-center px-3 py-2 rounded-xl transition-all duration-200 bg-white hover:bg-blue-50 active:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-200 shadow-sm hover:shadow-md text-gray-700 cursor-pointer"
        >
          <ChevronLeft
            size={16}
            className="text-gray-500 transition-transform duration-200 group-hover:-translate-x-0.5"
          />
          <span className="ml-2 text-sm font-medium text-gray-700 group-hover:text-blue-600">
            Back
          </span>
        </Button>

        <span className="text-sm font-medium text-muted-foreground tracking-wide">
          {project?.key}-{currentTicket?.id}
        </span>
      </div>

      <div className="space-y-6 rounded-lg bg-white px-6 pt-0.5 pb-6 shadow-sm">
          {/* Ticket Header */}
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 border-b pb-4">
            <div className="flex-1">
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3">
                {currentTicket.title}
              </h1>
              <div className="flex flex-wrap gap-2 items-center">
                <div className="w-32 sm:w-36 md:w-40">
                  <div className="relative">
                    <select
                      value={currentTicket.status}
                      className="w-full appearance-none rounded-md ring-1 ring-inset ring-gray-300 bg-gray-50 px-2.5 py-1.5 text-sm font-medium text-center text-gray-700 cursor-pointer hover:bg-gray-100 transition-colors pr-8"
                      onChange={(e) => {
                        const v = e.target.value;
                        handleChangeStatus(v);
                      }}
                    >
                      <option value="backlog">Backlog</option>
                      <option value="todo">To Do</option>
                      <option value="in-progress">In Progress</option>
                      <option value="review">Review</option>
                      <option value="done">Done</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                      <svg
                        className="h-4 w-4 text-gray-500"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                <PriorityBadge priority={currentTicket.priority} />
                <TypeBadge type={currentTicket.type} />
                {currentTicket.labels.map((label) => (
                  <span
                    key={label}
                    className="rounded-full bg-gray-100 px-2 py-0.5 text-xs text-gray-700"
                  >
                    {label}
                  </span>
                ))}
              </div>
            </div>

            {/* Actions - responsive */}
            <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                className="w-10 sm:w-auto px-2 sm:px-3 flex-shrink-0 flex items-center"
                aria-label="Open JIRA"
              >
                <Link2 size={16} />
                <span className="hidden md:inline ml-2 truncate">JIRA</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="w-10 sm:w-auto px-2 sm:px-3 flex-shrink-0 flex items-center"
                aria-label="Open GitHub PR"
              >
                <GitPullRequest size={16} />
                <span className="hidden md:inline ml-2 truncate">GitHub</span>
              </Button>

              <Button
                variant="primary"
                size="sm"
                className="w-10 sm:w-auto px-2 sm:px-3 flex-shrink-0 flex items-center justify-center"
              >
                <Edit size={16} />
                <span className="hidden md:inline ml-2 truncate">Edit</span>
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3 px-6">
          <div className="md:col-span-2 space-y-4">
            {/* Description */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h2 className="flex items-center text-lg font-semibold text-gray-900">
                  <AlignLeft size={18} className="mr-2 text-gray-600" />
                  Description
                </h2>
                {!isEditing && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                    onClick={() => setIsEditing(true)}
                  >
                    Edit
                  </Button>
                )}
              </div>

              {isEditing ? (
                <div className="space-y-3">
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full rounded-md border border-gray-300 bg-white p-3 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    rows={6}
                    placeholder="Add a detailed description..."
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setIsEditing(false);
                        setDescription(currentTicket.description);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handleSaveDescription}
                    >
                      Save
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="rounded-md bg-gray-50 p-4 text-sm text-gray-700 border border-gray-200">
                  {description || (
                    <span className="italic text-gray-400">
                      No description provided.
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Comments */}
            <div className="space-y-4">
              <h2 className="flex items-center text-lg font-semibold text-gray-900">
                <MessageSquare size={18} className="mr-2 text-gray-600" />
                Comments
              </h2>

              {/* Existing Comments */}
              <div className="space-y-4">
                {comments.length > 0 ? (
                  comments.map((comment) => (
                    <div
                      key={comment.id}
                      className="flex items-start space-x-3"
                    >
                      <Avatar
                        src={comment.user?.image_url}
                        alt={comment.user?.name || "User"}
                        size="sm"
                      />
                      <div className="flex-1 rounded-md border border-gray-200 bg-gray-50 p-3 shadow-sm">
                        <div className="mb-1 flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-800 truncate">
                            {comment.user?.name || "Unknown User"}
                          </span>
                          <span className="text-xs text-gray-400">
                            {comment.created_at &&
                            !isNaN(parseISO(comment.created_at).getTime())
                              ? format(
                                  parseISO(comment.created_at),
                                  "MMM d, yyyy h:mm a"
                                )
                              : "Invalid date"}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700 whitespace-pre-line">
                          {comment.content}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No comments yet.</p>
                )}
              </div>

              {/* Add Comment */}
              <div className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
                <textarea
                  placeholder="Add a comment..."
                  className="w-full resize-none bg-transparent p-2 text-sm text-gray-800 focus:outline-none focus:ring-0"
                  rows={3}
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                />
                <div className="flex justify-end border-t pt-2">
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                    className="flex items-center gap-1"
                  >
                    <Send size={14} /> Comment
                  </Button>
                </div>
              </div>
            </div>

            {/* Manage Relationships */}
            <div className="mt-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">
                  Manage Relationships
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddRelation((v) => !v)}
                  className="transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {showAddRelation ? "Cancel" : "Add Relationship"}
                </Button>
              </div>

              {showAddRelation && (
                <form
                  onSubmit={async (e) => {
                    e.preventDefault();
                    if (!selectedRelatedTicket || !selectedRelationType) return;
                    try {
                      const token = await getToken({
                        template: "supabase",
                      });
                      const { getSupabaseClient } = await import(
                        "@/integrations/supabase/client"
                      );
                      const supabase = getSupabaseClient(token);
                      await supabase.from("ticket_dependencies").insert({
                        ticket_id: ticketId,
                        related_ticket_id: selectedRelatedTicket,
                        relationship_type: selectedRelationType,
                      });
                      setShowAddRelation(false);
                      setSelectedRelatedTicket("");
                      setSelectedRelationType("relates to");
                      await fetchTicketRelationships();
                    } catch (err) {
                      toast({
                        title: "Error",
                        description: "Failed to add relationship.",
                        variant: "destructive",
                      });
                    }
                  }}
                  className="flex flex-col gap-3 md:flex-row md:items-center md:gap-4"
                >
                  <select
                    value={selectedRelationType}
                    onChange={(e) => setSelectedRelationType(e.target.value)}
                    className="w-full md:w-52 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm text-gray-700 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="blocks">blocks</option>
                    <option value="is blocked by">is blocked by</option>
                    <option value="duplicates">duplicates</option>
                    <option value="is duplicated by">is duplicated by</option>
                    <option value="implements">implements</option>
                    <option value="is implemented by">is implemented by</option>
                    <option value="is idea for">is idea for</option>
                    <option value="relates to">relates to</option>
                  </select>

                  <select
                    value={selectedRelatedTicket}
                    onChange={(e) => setSelectedRelatedTicket(e.target.value)}
                    className="w-full flex-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm text-gray-700 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="">Select ticket…</option>
                    {tickets
                      .filter(
                        (t) =>
                          t.id !== ticketId &&
                          t.projectId === currentTicket.projectId
                      )
                      .map((t) => (
                        <option key={t.id} value={t.id}>
                          {t.title}
                        </option>
                      ))}
                  </select>

                  <Button
                    type="submit"
                    size="sm"
                    variant="primary"
                    className="w-full md:w-auto"
                  >
                    Add
                  </Button>
                </form>
              )}
            </div>

            {/* Outgoing Relationships */}
            <div className="mt-6 space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Relationships
              </h2>
              {dependencies.length === 0 ? (
                <p className="text-sm text-gray-500">No relationships.</p>
              ) : (
                <ul className="grid grid-cols-1 gap-4">
                  {dependencies.map((rel) => (
                    <li
                      key={rel.id}
                      className="relative flex items-center justify-between rounded-xl border border-gray-200 bg-white p-4 pl-5 shadow-sm hover:bg-gray-50 transition-colors"
                    >
                      {/* Colored side accent bar */}
                      <span className="absolute left-0 top-0 h-full w-1 rounded-l bg-blue-500" />

                      {/* Relationship Info */}
                      <div className="flex items-center gap-3">
                        <Link2 className="h-4 w-4 text-gray-400" />
                        <span className="inline-flex items-center rounded-md bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-600 capitalize">
                          {rel.relationship_type}
                        </span>
                        <Link
                          to={`/tickets/${rel.related.id}`}
                          className="text-sm font-medium text-blue-600 hover:underline"
                        >
                          {rel.related.title}
                        </Link>
                      </div>

                      {/* Status Dot */}
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <CircleDot
                          className={`h-3 w-3 ${
                            rel.related.status === "open"
                              ? "text-green-500"
                              : "text-gray-400"
                          }`}
                          fill={
                            rel.related.status === "open"
                              ? "#22c55e"
                              : "transparent"
                          }
                        />
                        <span className="capitalize">{rel.related.status}</span>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* Incoming Relationships */}
            <div className="mt-6 space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Related By
              </h2>
              {blockedBy.length === 0 ? (
                <p className="text-sm text-gray-500">
                  No tickets relate to this one.
                </p>
              ) : (
                <ul className="grid grid-cols-1 gap-4">
                  {blockedBy.map((rel) => (
                    <li
                      key={rel.id}
                      className="relative flex items-center justify-between rounded-xl border border-gray-200 bg-white p-4 pl-5 shadow-sm hover:bg-gray-50 transition-colors"
                    >
                      <span className="absolute left-0 top-0 h-full w-1 rounded-l bg-violet-500" />

                      <div className="flex items-center gap-3">
                        <Link2 className="h-4 w-4 text-gray-400" />
                        <span className="inline-flex items-center rounded-md bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-600 capitalize">
                          {rel.relationship_type}
                        </span>
                        <Link
                          to={`/tickets/${rel.ticket.id}`}
                          className="text-sm font-medium text-blue-600 hover:underline"
                        >
                          {rel.ticket.title}
                        </Link>
                      </div>

                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <CircleDot
                          className={`h-3 w-3 ${
                            rel.ticket.status === "open"
                              ? "text-green-500"
                              : "text-gray-400"
                          }`}
                          fill={
                            rel.ticket.status === "open"
                              ? "#22c55e"
                              : "transparent"
                          }
                        />
                        <span className="capitalize">{rel.ticket.status}</span>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 rounded-xl bg-gray-50 p-4 border border-gray-100">
            {/* Details */}
            <div className="space-y-4">
              <h3 className="flex items-center gap-2 text-sm font-semibold text-gray-600 uppercase tracking-wide">
                <UserRound className="w-4 h-4 text-gray-400" />
                Details
              </h3>

              <div className="space-y-3 divide-y divide-gray-200">
                {/* Assignees */}
                <div className="flex justify-between items-start pb-2">
                  <span className="text-sm text-gray-500 pt-1">Assignees</span>
                  <div className="flex flex-col items-end space-y-1">
                    {ticketAssignees.length > 0 ? (
                      ticketAssignees.map((assignee) => (
                        <div key={assignee.id} className="flex items-center">
                          <Avatar
                            src={assignee.imageUrl}
                            alt={assignee.name}
                            size="xs"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {assignee.name || assignee.email}
                          </span>
                        </div>
                      ))
                    ) : (
                      <span className="text-sm text-gray-400">Unassigned</span>
                    )}
                  </div>
                </div>

                {/* Reporter */}
                {reporter && (
                  <div className="flex justify-between items-center pt-2 pb-2">
                    <span className="text-sm text-gray-500">Reporter</span>
                    <div className="flex items-center">
                      <Avatar
                        src={reporter.imageUrl}
                        alt={reporter.name}
                        size="xs"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {reporter.name}
                      </span>
                    </div>
                  </div>
                )}

                {/* Due Date */}
                {currentTicket.dueDate && (
                  <div className="flex justify-between items-center pt-2 pb-2">
                    <span className="text-sm text-gray-500">Due Date</span>
                    <div className="flex items-center text-sm text-gray-700">
                      <Calendar size={14} className="mr-1 text-gray-400" />
                      {format(parseISO(currentTicket.dueDate), "MMM d, yyyy")}
                    </div>
                  </div>
                )}

                {/* Estimated */}
                {currentTicket.estimatedHours !== undefined && (
                  <div className="flex justify-between items-center pt-2 pb-2">
                    <span className="text-sm text-gray-500">Estimated</span>
                    <div className="flex items-center text-sm text-gray-700">
                      <Clock size={14} className="mr-1 text-gray-400" />
                      {currentTicket.estimatedHours}h
                    </div>
                  </div>
                )}

                {/* Created */}
                <div className="flex justify-between items-center pt-2 pb-2">
                  <span className="text-sm text-gray-500">Created</span>
                  <span className="text-sm text-gray-700">
                    {currentTicket.createdAt &&
                    !isNaN(parseISO(currentTicket.createdAt).getTime())
                      ? format(parseISO(currentTicket.createdAt), "MMM d, yyyy")
                      : "N/A"}
                  </span>
                </div>

                {/* Updated */}
                <div className="flex justify-between items-center pt-2">
                  <span className="text-sm text-gray-500">Updated</span>
                  <span className="text-sm text-gray-700">
                    {currentTicket.updatedAt &&
                    !isNaN(parseISO(currentTicket.updatedAt).getTime())
                      ? format(parseISO(currentTicket.updatedAt), "MMM d, yyyy")
                      : "N/A"}
                  </span>
                </div>
              </div>
            </div>

            {/* Pull Requests */}
            {currentTicket.githubPRs.length > 0 && (
              <div className="space-y-3">
                <h3 className="flex items-center gap-2 text-sm font-semibold text-gray-600 uppercase tracking-wide">
                  <GitPullRequest className="w-4 h-4 text-purple-500" />
                  Pull Requests
                </h3>
                <ul className="space-y-2">
                  {currentTicket.githubPRs.map((pr) => (
                    <li key={pr.id}>
                      <a
                        href={pr.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-between rounded-md border border-gray-200 bg-white p-2 text-sm hover:bg-gray-50 transition"
                      >
                        <div className="flex items-center gap-2 overflow-hidden">
                          <GitPullRequest
                            size={14}
                            className="text-purple-500 shrink-0"
                          />
                          <span className="truncate">{pr.title}</span>
                        </div>
                        <span
                          className={`ml-2 text-xs font-medium ${
                            pr.status === "open"
                              ? "text-green-600"
                              : pr.status === "closed"
                              ? "text-red-600"
                              : "text-gray-500"
                          }`}
                        >
                          {pr.status}
                        </span>
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
   
  );
};

export default TicketDetail;
