import { useState, useEffect, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import AppLayout from "@/components/layouts/AppLayout";
import { useProjectStore } from "@/store/projectStore";
import { useAuth } from "@clerk/clerk-react";
import { getSupabaseClient } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAuthContext } from "@/context/AuthContext";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { linkJiraProject, setupJiraWebhook } from "@/lib/jiraSync";
import { useToast } from "@/hooks/use-toast";
import {
  Key,
  Settings as SettingsIcon,
  Database,
  Bell,
  Users,
  UserMinus,
  Loader2,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { useUserStore } from "@/store/userStore";
import { Project, User } from "@/types";

const Settings = () => {
  const { getCurrentUserUuid } = useUserStore();
  const { getToken } = useAuth();
  const navigate = useNavigate();
  const { projectId } = useParams();
  const { userRole } = useAuthContext();
  const { toast } = useToast();
  const {
    projects,
    fetchProjects,
    updateProject,
    currentProject,
    setCurrentProject,
  } = useProjectStore();
  const [activeTab, setActiveTab] = useState("general");
  const [editedProject, setEditedProject] = useState({
    name: "",
    key: "",
    description: "",
  });
  const [jiraKey, setJiraKey] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const { users } = useUserStore();

  const projectMembers = useMemo((): User[] => {
    if (!currentProject?.members || !users.length) {
      return [];
    }

    const members = currentProject.members;

    if (!Array.isArray(members) || members.length === 0) {
      return [];
    }

    const firstMember = members[0];

    if (typeof firstMember === "string") {
      const memberIds = members as string[];
      return users.filter((u) => memberIds.includes(u.id));
    }

    if (typeof firstMember === "object" && firstMember !== null) {
      const idKey = "user_id" in firstMember ? "user_id" : "id";
      if (idKey in firstMember) {
        const memberIds = members.map((m: any) => m[idKey]);
        return users.filter((u) => memberIds.includes(u.id));
      }
    }

    return [];
  }, [currentProject, users]);

  const { userId: clerkUserId } = useAuth();
  const [currentUserDbUuid, setCurrentUserDbUuid] = useState<string | null>(
    null
  );

  useEffect(() => {
    const loadProject = async () => {
      setIsLoading(true);
      try {
        const token = await getToken({
          template:
            "supabase",
        });
        const supabase = getSupabaseClient(token);
        const allProjects = await fetchProjects(supabase);

        if (clerkUserId) {
          const dbUuid = await getCurrentUserUuid(clerkUserId, supabase);
          setCurrentUserDbUuid(dbUuid);
        }

        if (projectId) {
          // Find the project by id (uuid) or key
          let project;
          if (
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
              projectId
            )
          ) {
            project = allProjects.find((p) => p.id === projectId);
          } else {
            project = allProjects.find(
              (p) => p.key?.toLowerCase() === projectId.toLowerCase()
            );
          }

          if (project) {
            setCurrentProject(project);
            setEditedProject({
              name: project.name,
              key: project.key || "",
              description: project.description,
            });
          } else {
            toast({
              title: "Project not found",
              description: "The requested project could not be found.",
              variant: "destructive",
            });
            navigate("/settings");
          }
        }
      } catch (error) {
        console.error("Failed to load project:", error);
        toast({
          title: "Error",
          description: "Failed to load project details",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadProject();
  }, [
    projectId,
    fetchProjects,
    getToken,
    navigate,
    setCurrentProject,
    toast,
    clerkUserId,
    getCurrentUserUuid,
  ]);

  const canEditProject = useMemo(() => {
    if (userRole === "admin") return true;
    if (!currentProject || !currentUserDbUuid) return false;
    const members = (currentProject.members as Project["members"]) || [];
    const member = members.find((m) => m.user_id === currentUserDbUuid);
    return member?.role === "project_manager";
  }, [currentProject, currentUserDbUuid, userRole]);

  const handleSaveGeneral = async () => {
    if (!currentProject) return;

    setIsSaving(true);
    try {
      const token = await getToken({
        template:
          "supabase",
      });
      const supabase = getSupabaseClient(token);

      await updateProject(
        {
          id: currentProject.id,
          name: editedProject.name,
          key: editedProject.key.toUpperCase(),
          description: editedProject.description,
        },
        supabase
      );

      toast({
        title: "Success",
        description: "Project settings saved successfully",
        variant: "success",
      });

      // If the key changed, redirect to the new URL
      if (editedProject.key && editedProject.key !== currentProject.key) {
        navigate(`/settings/projects/${editedProject.key}`);
      }
    } catch (error) {
      console.error("Failed to save project settings:", error);
      toast({
        title: "Error",
        description: "Failed to save project settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleConnectJira = async () => {
    if (!currentProject || !jiraKey) return;

    setIsSaving(true);
    try {
      const token = await getToken({
        template:
          "supabase",
      });
      const supabase = getSupabaseClient(token);

      const { jiraProjectId } = await linkJiraProject(
        supabase,
        currentProject.id,
        jiraKey
      );
      await setupJiraWebhook(supabase, currentProject.id, jiraProjectId);

      // Update project key if it doesn't have one
      if (!currentProject.key) {
        await updateProject(currentProject.id, { key: jiraKey }, supabase);
        setEditedProject((prev) => ({
          ...prev,
          key: jiraKey,
        }));
      }

      toast({
        title: "Success",
        description: "JIRA integration configured successfully",
        variant: "success",
      });
      setJiraKey("");

      // Refresh projects to get updated data
      await fetchProjects(supabase);
    } catch (error) {
      console.error("Failed to connect to JIRA:", error);
      toast({
        title: "Error",
        description: "Failed to connect to JIRA",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (!isLoading && !canEditProject) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <Card className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 max-w-2xl mx-auto">
            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-red-50/30 via-transparent to-orange-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            <CardHeader className="relative p-6 border-b border-gray-100">
              <div className="flex items-center gap-3 mb-1">
                <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                  <SettingsIcon className="h-4 w-4" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">
                  Access Denied
                </CardTitle>
              </div>
              <CardDescription className="text-gray-600">
                You need administrator permissions to access settings.
              </CardDescription>
            </CardHeader>
            <CardContent className="relative p-6">
              <Button
                onClick={() => navigate("/dashboard")}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
              >
                Return to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-4xl font-bold text-gray-800 flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center text-white shadow-sm">
              <SettingsIcon className="h-5 w-5" />
            </div>
            {projectId
              ? `Project Settings: ${currentProject?.name || "Loading..."}`
              : "Settings"}
          </h1>
        </div>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="bg-gray-100 p-1 rounded-lg border border-gray-200 shadow-sm">
          <TabsTrigger
            value="general"
            className="data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-gray-200 text-gray-600 hover:text-gray-800 transition-colors duration-200 font-medium"
          >
            General
          </TabsTrigger>
          <TabsTrigger
            value="integrations"
            className="data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-gray-200 text-gray-600 hover:text-gray-800 transition-colors duration-200 font-medium"
          >
            Integrations
          </TabsTrigger>
          <TabsTrigger
            value="members"
            className="data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-gray-200 text-gray-600 hover:text-gray-800 transition-colors duration-200 font-medium"
          >
            Members
          </TabsTrigger>
          <TabsTrigger
            value="notifications"
            className="data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-gray-200 text-gray-600 hover:text-gray-800 transition-colors duration-200 font-medium"
          >
            Notifications
          </TabsTrigger>
        </TabsList>

        {isLoading ? (
          <Card className="group relative overflow-hidden bg-white border border-gray-200 rounded-xl shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-center h-48">
                <div className="text-center">
                  <div className="w-12 h-12 border-4 border-blue-500 border-dashed rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-gray-500 font-medium">
                    Loading settings...
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            <TabsContent value="general" className="space-y-6">
              {projectId && currentProject ? (
                <Card className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
                  {/* Subtle gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <CardHeader className="relative p-6 border-b border-gray-100">
                    <div className="flex items-center gap-3 mb-1">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                        {currentProject.name.charAt(0).toUpperCase()}
                      </div>
                      <CardTitle className="text-xl font-semibold text-gray-900">
                        Project Details
                      </CardTitle>
                    </div>
                    <CardDescription className="text-gray-600">
                      Manage this project's basic information
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="relative p-6 space-y-6">
                    <div className="space-y-3">
                      <Label
                        htmlFor="project-name"
                        className="text-sm font-semibold text-gray-700"
                      >
                        Project Name
                      </Label>
                      <Input
                        id="project-name"
                        value={editedProject.name}
                        onChange={(e) =>
                          setEditedProject({
                            ...editedProject,
                            name: e.target.value,
                          })
                        }
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label
                        htmlFor="project-key"
                        className="text-sm font-semibold text-gray-700"
                      >
                        Project Key
                      </Label>
                      <Input
                        id="project-key"
                        value={editedProject.key}
                        onChange={(e) =>
                          setEditedProject({
                            ...editedProject,
                            key: e.target.value.toUpperCase(),
                          })
                        }
                        maxLength={10}
                        placeholder="e.g. PROJ"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg font-mono"
                      />
                      <p className="text-sm text-gray-500">
                        A short, unique identifier for this project (max 10
                        characters).
                      </p>
                    </div>

                    <div className="space-y-3">
                      <Label
                        htmlFor="project-description"
                        className="text-sm font-semibold text-gray-700"
                      >
                        Description
                      </Label>
                      <Textarea
                        id="project-description"
                        value={editedProject.description}
                        onChange={(e) =>
                          setEditedProject({
                            ...editedProject,
                            description: e.target.value,
                          })
                        }
                        placeholder="Project description"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg min-h-[100px]"
                      />
                    </div>

                    <Button
                      onClick={handleSaveGeneral}
                      disabled={isSaving}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                    >
                      {isSaving ? "Saving..." : "Save Changes"}
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <Card className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
                  {/* Subtle gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-50/30 via-transparent to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <CardHeader className="relative p-6 border-b border-gray-100">
                    <div className="flex items-center gap-3 mb-1">
                      <div className="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                        <SettingsIcon className="h-4 w-4" />
                      </div>
                      <CardTitle className="text-xl font-semibold text-gray-900">
                        Global Settings
                      </CardTitle>
                    </div>
                    <CardDescription className="text-gray-600">
                      Manage system-wide settings and configurations
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="relative p-6">
                    <p className="text-gray-600 mb-6">
                      Select a project from the Projects page to manage
                      project-specific settings, or use the tabs above to
                      configure global settings.
                    </p>
                    <Button
                      onClick={() => navigate("/projects")}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                    >
                      Go to Projects
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="integrations" className="space-y-6">
              {projectId && currentProject ? (
                <>
                  <Card className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
                    {/* Subtle gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    <CardHeader className="relative p-6 border-b border-gray-100">
                      <div className="flex items-center gap-3 mb-1">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                          <Key className="h-4 w-4" />
                        </div>
                        <CardTitle className="text-xl font-semibold text-gray-900">
                          JIRA Integration
                        </CardTitle>
                      </div>
                      <CardDescription className="text-gray-600">
                        Connect this project to JIRA for ticket synchronization
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="relative p-6 space-y-6">
                      {currentProject?.jiraProjectId ? (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                            <p className="text-sm text-green-700 font-semibold">
                              Connected to JIRA
                            </p>
                          </div>
                          <p className="text-sm text-gray-600">
                            This project is synchronized with JIRA project key:{" "}
                            <Badge
                              variant="outline"
                              className="font-mono text-xs bg-gray-50 text-gray-700 border-gray-200"
                            >
                              {currentProject?.key}
                            </Badge>
                          </p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="space-y-3">
                            <Label
                              htmlFor="jira-key"
                              className="text-sm font-semibold text-gray-700"
                            >
                              JIRA Project Key
                            </Label>
                            <Input
                              id="jira-key"
                              value={jiraKey}
                              onChange={(e) =>
                                setJiraKey(e.target.value.toUpperCase())
                              }
                              placeholder="e.g. PROJ"
                              maxLength={10}
                              className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg font-mono"
                            />
                            <p className="text-sm text-gray-500">
                              Enter the project key from your JIRA instance.
                            </p>
                          </div>

                          <Button
                            onClick={handleConnectJira}
                            disabled={isSaving || !jiraKey}
                            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {isSaving ? "Connecting..." : "Connect to JIRA"}
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
                    {/* Subtle gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-50/30 via-transparent to-slate-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    <CardHeader className="relative p-6 border-b border-gray-100">
                      <div className="flex items-center gap-3 mb-1">
                        <div className="w-8 h-8 bg-gradient-to-br from-gray-700 to-gray-800 rounded-lg flex items-center justify-center text-white shadow-sm">
                          <Database className="h-4 w-4" />
                        </div>
                        <CardTitle className="text-xl font-semibold text-gray-900">
                          GitHub Integration
                        </CardTitle>
                      </div>
                      <CardDescription className="text-gray-600">
                        Connect this project to GitHub for code integration
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="relative p-6">
                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <p className="text-gray-600">
                          Connect a GitHub repository to link pull requests to
                          tickets in this project.
                        </p>
                        <p className="text-sm text-gray-500 mt-2">
                          Coming soon - GitHub integration is currently under
                          development.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <Card className="group relative overflow-hidden bg-white border border-gray-200 rounded-xl shadow-sm">
                  <CardContent className="p-6">
                    <div className="text-center py-8">
                      <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3 mx-auto">
                        <Key className="w-6 h-6 text-gray-400" />
                      </div>
                      <p className="text-gray-500 font-medium">
                        No project selected
                      </p>
                      <p className="text-sm text-gray-400 mt-1">
                        Select a project to manage integrations
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="members" className="space-y-6">
              {projectId && currentProject ? (
                <Card className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
                  {/* Subtle gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-50/30 via-transparent to-indigo-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <CardHeader className="relative p-6 border-b border-gray-100">
                    <div className="flex items-center gap-3 mb-1">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                        <Users className="h-4 w-4" />
                      </div>
                      <CardTitle className="text-xl font-semibold text-gray-900">
                        Project Members
                      </CardTitle>
                    </div>
                    <CardDescription className="text-gray-600">
                      Manage who has access to this project
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="relative p-6 space-y-6">
                    {currentProject.members.length === 0 ? (
                      <div className="text-center py-8">
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3 mx-auto">
                          <Users className="w-6 h-6 text-gray-400" />
                        </div>
                        <p className="text-gray-500 font-medium">
                          No members in this project yet
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {projectMembers.map((member) => {
                          return (
                            <div
                              key={member.id}
                              className="flex items-center justify-between p-4 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 hover:border-gray-300 transition-all duration-200"
                            >
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center text-white text-sm font-semibold shadow-sm">
                                  {member.name?.charAt(0).toUpperCase() ||
                                    member.email?.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                  <span className="font-medium text-gray-900">
                                    {member?.name || member.email}
                                  </span>
                                  <p className="text-sm text-gray-500">
                                    {member.email}
                                  </p>
                                </div>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  const token = await getToken({
                                    template:
                                      import.meta.env.MODE === "production"
                                        ? "supabase"
                                        : "supabase",
                                  });
                                  const supabase = getSupabaseClient(token);
                                  await useProjectStore
                                    .getState()
                                    .removeProjectMember(
                                      currentProject.id,
                                      member.id,
                                      supabase
                                    );
                                  await fetchProjects(supabase);
                                  toast({
                                    title: "Success",
                                    description: "Member removed from project",
                                    variant: "success",
                                  });
                                }}
                                disabled={isSaving}
                                className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 hover:text-red-700 transition-colors duration-200 cursor-pointer"
                              >
                                <UserMinus className="h-4 w-4 mr-1" />
                                Remove
                              </Button>
                            </div>
                          );
                        })}
                      </div>
                    )}

                    <Separator className="bg-gray-200" />

                    <div className="space-y-3">
                      <Label
                        htmlFor="add-member-select"
                        className="text-sm font-semibold text-gray-700"
                      >
                        Add member to project
                      </Label>
                      <div className="flex gap-3 items-center">
                        <select
                          id="add-member-select"
                          className="flex-1 border border-gray-300 rounded-lg px-3 py-2 bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          disabled={isAddingMember}
                          onChange={async (e) => {
                            const userId = e.target.value;
                            if (userId) {
                              setIsAddingMember(true);
                              try {
                                const user = users.find((u) => u.id === userId);
                                const token = await getToken({
                                  template:
                                    import.meta.env.MODE === "production"
                                      ? "supabase"
                                      : "supabase",
                                });
                                const supabase = getSupabaseClient(token);
                                await useProjectStore
                                  .getState()
                                  .addProjectMember(
                                    currentProject.id,
                                    userId,
                                    user?.role || "developer",
                                    supabase
                                  );
                                await fetchProjects(supabase);
                                toast({
                                  title: "Success",
                                  description: "Member added to project",
                                  variant: "success",
                                });
                              } catch (error) {
                                console.error("Failed to add member:", error);
                                toast({
                                  title: "Error",
                                  description:
                                    "Failed to add member to project.",
                                  variant: "destructive",
                                });
                              } finally {
                                e.target.value = "";
                                setIsAddingMember(false);
                              }
                            }
                          }}
                          defaultValue=""
                          aria-label="Select user to add to project"
                        >
                          <option value="">Select user to add</option>
                          {users
                            .filter(
                              (u) => !projectMembers.some((m) => m.id === u.id)
                            )
                            .map((u) => (
                              <option key={u.id} value={u.id}>
                                {u.name} ({u.email})
                              </option>
                            ))}
                        </select>
                        {isAddingMember && (
                          <Loader2 className="h-5 w-5 animate-spin text-gray-500" />
                        )}
                      </div>
                      <p className="text-sm text-gray-500">
                        Select a user from the dropdown to add them to this
                        project.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
                  {/* Subtle gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-50/30 via-transparent to-indigo-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <CardHeader className="relative p-6 border-b border-gray-100">
                    <div className="flex items-center gap-3 mb-1">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                        <Users className="h-4 w-4" />
                      </div>
                      <CardTitle className="text-xl font-semibold text-gray-900">
                        Members
                      </CardTitle>
                    </div>
                    <CardDescription className="text-gray-600">
                      View all users, projects they belong to and their roles
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="relative p-6">
                    <Button
                      onClick={() => navigate("/members")}
                      className="bg-purple-600 hover:bg-purple-700 text-white font-semibold px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                    >
                      Go to Members
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card className="group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
                {/* Subtle gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-50/30 via-transparent to-orange-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                <CardHeader className="relative p-6 border-b border-gray-100">
                  <div className="flex items-center gap-3 mb-1">
                    <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg flex items-center justify-center text-white shadow-sm">
                      <Bell className="h-4 w-4" />
                    </div>
                    <CardTitle className="text-xl font-semibold text-gray-900">
                      Notification Settings
                    </CardTitle>
                  </div>
                  <CardDescription className="text-gray-600">
                    Configure how and when you receive notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="relative p-6">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between p-4 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 hover:border-gray-300 transition-all duration-200">
                      <Label
                        htmlFor="email-notifs"
                        className="flex flex-col gap-1 cursor-pointer"
                      >
                        <span className="font-semibold text-gray-900">
                          Email Notifications
                        </span>
                        <span className="font-normal text-sm text-gray-600">
                          Receive updates about this project via email
                        </span>
                      </Label>
                      <Switch
                        id="email-notifs"
                        checked={notificationsEnabled}
                        onCheckedChange={setNotificationsEnabled}
                        className="data-[state=checked]:bg-blue-600"
                      />
                    </div>

                    <Separator className="bg-gray-200" />

                    <div className="flex items-center justify-between p-4 bg-gray-50 border border-gray-200 rounded-lg opacity-60">
                      <Label
                        htmlFor="slack-notifs"
                        className="flex flex-col gap-1"
                      >
                        <span className="font-semibold text-gray-700">
                          Slack Notifications
                        </span>
                        <span className="font-normal text-sm text-gray-500">
                          Send updates to a Slack channel (Coming soon)
                        </span>
                      </Label>
                      <Switch id="slack-notifs" checked={false} disabled />
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-50 border border-gray-200 rounded-lg opacity-60">
                      <Label
                        htmlFor="teams-notifs"
                        className="flex flex-col gap-1"
                      >
                        <span className="font-semibold text-gray-700">
                          Microsoft Teams
                        </span>
                        <span className="font-normal text-sm text-gray-500">
                          Send updates to a Teams channel (Coming soon)
                        </span>
                      </Label>
                      <Switch id="teams-notifs" checked={false} disabled />
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Bell className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-blue-900 mb-1">
                            Notification Preferences
                          </h4>
                          <p className="text-sm text-blue-700">
                            You'll receive notifications for ticket assignments,
                            status changes, comments, and project updates. You
                            can customize these preferences in your user profile
                            settings.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        )}
      </Tabs>
    </AppLayout>
  );
};

export default Settings;
