version: 2.1

orbs:
  node: circleci/node@5.2
  docker: circleci/docker@2.1.1

jobs:
  test:
    docker:
      - image: cimg/node:23.10.0
    steps:
      - checkout
      - node/install-packages:
          pkg-manager: yarn
          cache-path: ~/project/node_modules
          include-branch-in-cache-key: false
      - run:
          name: Run ESLint
          command: yarn lint
      - run:
          name: Run Tests
          command: yarn test
      
  build:
    docker:
      - image: cimg/node:23.10.0
    steps:
      - checkout
      - node/install-packages:
          pkg-manager: yarn
          cache-path: ~/project/node_modules
          include-branch-in-cache-key: false
      - run:
          name: Build Application
          command: yarn build
      - persist_to_workspace:
          root: .
          paths:
            - .next
            - node_modules
            - package.json
            - yarn.lock

  migrate_database:
    docker:
      - image: cimg/node:23.10.0
    steps:
      - checkout
      - node/install-packages:
          pkg-manager: yarn
          cache-path: ~/project/node_modules
          include-branch-in-cache-key: false
      - run:
          name: Link Supabase Project
          command: npx supabase link --project-ref ${SUPABASE_PROJECT_ID}
          environment:
            SUPABASE_ACCESS_TOKEN: ${SUPABASE_ACCESS_TOKEN}
      - run:
          name: Run Database Migrations
          command: npx supabase db push --password ${SUPABASE_DB_PASSWORD}

  deploy:
    docker:
      - image: cimg/node:23.10.0
    steps:
      - checkout
      - attach_workspace:
          at: .
      - run:
          name: Install Deployment Dependencies
          command: |
            sudo npm install -g vercel
      - run:
          name: Deploy to Production
          command: |
            if [ "${CIRCLE_BRANCH}" == "main" ]; then
              vercel --prod --token ${VERCEL_TOKEN}
            else
              vercel --token ${VERCEL_TOKEN}
            fi

workflows:
  version: 2
  build-test-deploy:
    jobs:
      - test
      - migrate_database:
          requires:
            - test
      - build:
          requires:
            - migrate_database
      - deploy:
          requires:
            - build
          filters:
            branches:
              only:
                - main
                - staging