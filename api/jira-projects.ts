import type { VercelRequest, VercelResponse } from "@vercel/node";

const JIRA_PROJECT_API_BASE_URL =
  "https://sparkstrand.atlassian.net/rest/api/3/project";
const JIRA_PROJECT_SEARCH_API_URL =
  "https://sparkstrand.atlassian.net/rest/api/3/project/search";
const JIRA_API_KEY =
  "ATATT3xFfGF0IGnE9aU0yu0RV51eae-Lrd1wCCW4wTamhjecwBLIa6PpvBWDuwqqD5pCWhUXmBfH5tZB69SHqX1OBlGHs6lTUKyQXBjf3cKeafsV-pnhCFTOAFNS6HYWgjwXuavsK9RMpzY7WKZz5B2oPVqf9cY5pv709MNBCyREziImLIq0j7U=1013AA99";
const JIRA_EMAIL = "<EMAIL>";

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Add CORS headers for development
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  const { name, key } = req.query;

  // Validate required auth environment variables
  if (!JIRA_API_KEY || !JIRA_EMAIL) {
    console.error("Missing JIRA API credentials");
    return res
      .status(500)
      .json({ error: "Server configuration error: missing JIRA credentials" });
  }

  try {
    // Case 1: Lookup by name
    if (name) {
      console.log(`Looking up JIRA project by name: ${name}`);
      const searchName = String(name);

      const jiraRes = await fetch(
        `${JIRA_PROJECT_SEARCH_API_URL}?query=${encodeURIComponent(
          searchName
        )}`,
        {
          headers: {
            Authorization: `Basic ${Buffer.from(
              `${JIRA_EMAIL}:${JIRA_API_KEY}`
            ).toString("base64")}`,
            Accept: "application/json",
          },
        }
      );

      if (!jiraRes.ok) {
        console.error(
          `JIRA API returned ${jiraRes.status}: ${jiraRes.statusText}`
        );
        return res
          .status(jiraRes.status)
          .json({ error: "JIRA fetch failed", details: jiraRes.statusText });
      }

      const searchResult = await jiraRes.json();
      let found = null;

      // The search API returns a list under 'values'.
      // We still need to ensure an exact name match as JIRA search can be broad.
      if (searchResult && Array.isArray(searchResult.values)) {
        found = searchResult.values.find(
          (p: any) => p.name?.toLowerCase() === searchName.toLowerCase()
        );
      }

      console.log(
        found ? `Found project with key: ${found.key}` : "No project found"
      );
      res.status(200).json({ key: found?.key || null });
      // Explicitly return to ensure no further code in this function is executed.
      return;
    }
    // Case 2: Lookup by key
    else if (key) {
      console.log(`Looking up JIRA project by key: ${key}`);

      const jiraRes = await fetch(
        `${JIRA_PROJECT_API_BASE_URL}/${String(key)}`,
        {
          headers: {
            Authorization: `Basic ${Buffer.from(
              `${JIRA_EMAIL}:${JIRA_API_KEY}`
            ).toString("base64")}`,
            Accept: "application/json",
          },
        }
      );

      if (jiraRes.status === 404) {
        console.log(`No project found with key: ${key}`);
        res.status(200).json({ exists: false });
        // Explicitly return
        return;
      }

      if (!jiraRes.ok) {
        console.error(
          `JIRA API returned ${jiraRes.status}: ${jiraRes.statusText}`
        );
        return res
          .status(jiraRes.status)
          .json({ error: "JIRA fetch failed", details: jiraRes.statusText });
      }

      const project = await jiraRes.json();
      console.log(`Found project: ${project.name}`);
      res.status(200).json({ exists: true, name: project.name });
      // Explicitly return
      return;
    }
    // No parameters provided
    else {
      res.status(400).json({ error: "Missing project name or key" });
      // Explicitly return
      return;
    }
  } catch (e) {
    console.error("Error in JIRA API handler:", e);
    res
      .status(500)
      .json({ error: "Internal server error", details: String(e) });
    // Explicitly return after sending error response
    return;
  }
}
